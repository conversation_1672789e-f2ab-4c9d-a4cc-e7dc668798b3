#!/usr/bin/env python3
"""
Script setup tự động cho Unica Clone
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

class UnicaSetup:
    def __init__(self):
        self.project_root = Path.cwd()
        self.backend_dir = self.project_root / "backend"
        self.realtime_dir = self.project_root / "realtime"
        self.database_dir = self.project_root / "database"
        
    def run_command(self, command, cwd=None, shell=True):
        """Chạy command và hiển thị output"""
        try:
            print(f"🔧 Chạy: {command}")
            result = subprocess.run(
                command, 
                cwd=cwd, 
                shell=shell, 
                check=True,
                capture_output=True,
                text=True
            )
            if result.stdout:
                print(result.stdout)
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Lỗi: {e}")
            if e.stderr:
                print(f"Chi tiết lỗi: {e.stderr}")
            return False
    
    def setup_backend(self):
        """Thiết lập backend"""
        print("\n📦 Thiết lập Backend (FastAPI)...")
        
        # Tạo virtual environment
        venv_path = self.backend_dir / "venv"
        if not venv_path.exists():
            print("🔧 Tạo virtual environment...")
            if not self.run_command(f"{sys.executable} -m venv venv", cwd=self.backend_dir):
                return False
        
        # Xác định đường dẫn pip
        if os.name == 'nt':  # Windows
            pip_path = venv_path / "Scripts" / "pip"
            python_path = venv_path / "Scripts" / "python"
        else:  # macOS/Linux
            pip_path = venv_path / "bin" / "pip"
            python_path = venv_path / "bin" / "python"
        
        # Cài đặt dependencies
        print("📦 Cài đặt Python dependencies...")
        if not self.run_command(f"{pip_path} install -r requirements.txt", cwd=self.backend_dir):
            return False
        
        # Tạo .env file
        env_file = self.backend_dir / ".env"
        env_example = self.backend_dir / ".env.example"
        
        if not env_file.exists() and env_example.exists():
            print("📝 Tạo file .env...")
            shutil.copy(env_example, env_file)
            print("⚠️  Vui lòng chỉnh sửa file backend/.env với thông tin database của bạn")
        
        return True
    
    def setup_realtime(self):
        """Thiết lập realtime server"""
        print("\n📡 Thiết lập Realtime Server (Node.js)...")
        
        # Cài đặt dependencies
        print("📦 Cài đặt Node.js dependencies...")
        if not self.run_command("npm install", cwd=self.realtime_dir):
            return False
        
        # Tạo .env file
        env_file = self.realtime_dir / ".env"
        env_example = self.realtime_dir / ".env.example"
        
        if not env_file.exists() and env_example.exists():
            print("📝 Tạo file .env...")
            shutil.copy(env_example, env_file)
        
        return True
    
    def setup_database(self):
        """Thiết lập database"""
        print("\n🗄️  Thiết lập Database...")
        
        # Kiểm tra PostgreSQL
        try:
            result = subprocess.run(["psql", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ PostgreSQL không được tìm thấy")
                return False
        except FileNotFoundError:
            print("❌ PostgreSQL không được tìm thấy")
            return False
        
        print("✅ PostgreSQL đã được cài đặt")
        
        # Hướng dẫn tạo database
        print("\n📋 Hướng dẫn tạo database:")
        print("1. Mở terminal/command prompt mới")
        print("2. Chạy các lệnh sau:")
        print("   sudo -u postgres psql  # Linux/macOS")
        print("   psql -U postgres       # Windows")
        print("3. Trong PostgreSQL shell:")
        print("   CREATE DATABASE unica_clone;")
        print("   CREATE USER unica_user WITH PASSWORD 'your_password';")
        print("   GRANT ALL PRIVILEGES ON DATABASE unica_clone TO unica_user;")
        print("   \\q")
        print("4. Import schema:")
        print("   psql -U unica_user -d unica_clone -f database/schema.sql")
        print("   psql -U unica_user -d unica_clone -f database/sample_data.sql")
        
        return True
    
    def create_start_script(self):
        """Tạo script khởi động"""
        print("\n📝 Tạo script khởi động...")
        
        if os.name == 'nt':  # Windows
            script_content = """@echo off
echo Starting Unica Clone Development Environment...

echo Starting Backend...
start "Backend" cmd /k "cd backend && venv\\Scripts\\activate && uvicorn main:app --reload --port 8000"

timeout /t 3

echo Starting Realtime Server...
start "Realtime" cmd /k "cd realtime && npm run dev"

timeout /t 2

echo Starting Frontend...
start "Frontend" cmd /k "cd frontend && python -m http.server 3000"

echo.
echo All services started!
echo Frontend: http://localhost:3000
echo Backend: http://localhost:8000
echo API Docs: http://localhost:8000/docs
echo.
pause
"""
            script_path = self.project_root / "start.bat"
        else:  # macOS/Linux
            script_content = """#!/bin/bash
echo "Starting Unica Clone Development Environment..."

# Start Backend
echo "Starting Backend..."
cd backend
source venv/bin/activate
uvicorn main:app --reload --port 8000 &
BACKEND_PID=$!
cd ..

sleep 3

# Start Realtime Server
echo "Starting Realtime Server..."
cd realtime
npm run dev &
REALTIME_PID=$!
cd ..

sleep 2

# Start Frontend
echo "Starting Frontend..."
cd frontend
python3 -m http.server 3000 &
FRONTEND_PID=$!
cd ..

echo ""
echo "All services started!"
echo "Frontend: http://localhost:3000"
echo "Backend: http://localhost:8000"
echo "API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for interrupt
trap 'kill $BACKEND_PID $REALTIME_PID $FRONTEND_PID; exit' INT
wait
"""
            script_path = self.project_root / "start.sh"
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        if os.name != 'nt':
            os.chmod(script_path, 0o755)
        
        print(f"✅ Đã tạo script khởi động: {script_path}")
        return True
    
    def run_setup(self):
        """Chạy toàn bộ setup"""
        print("🌟 Unica Clone - Setup tự động")
        print("=" * 50)
        
        try:
            # Setup backend
            if not self.setup_backend():
                print("❌ Setup backend thất bại")
                return False
            
            # Setup realtime
            if not self.setup_realtime():
                print("❌ Setup realtime thất bại")
                return False
            
            # Setup database
            if not self.setup_database():
                print("❌ Setup database thất bại")
                return False
            
            # Tạo start script
            if not self.create_start_script():
                print("❌ Tạo start script thất bại")
                return False
            
            print("\n" + "=" * 50)
            print("🎉 Setup hoàn tất!")
            print("=" * 50)
            print("📋 Các bước tiếp theo:")
            print("1. Chỉnh sửa file backend/.env với thông tin database")
            print("2. Tạo database PostgreSQL theo hướng dẫn ở trên")
            print("3. Chạy dự án:")
            if os.name == 'nt':
                print("   - Windows: Chạy start.bat")
            else:
                print("   - macOS/Linux: Chạy ./start.sh")
            print("   - Hoặc: python run.py")
            print("=" * 50)
            
            return True
            
        except Exception as e:
            print(f"❌ Lỗi setup: {e}")
            return False

def main():
    setup = UnicaSetup()
    success = setup.run_setup()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

// API Service for Unica Clone

class ApiService {
    constructor() {
        this.baseURL = CONFIG.API.BASE_URL;
        this.timeout = CONFIG.API.TIMEOUT;
        this.retryAttempts = CONFIG.API.RETRY_ATTEMPTS;
    }

    // Get authentication headers
    getAuthHeaders() {
        const token = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
        return token ? { 'Authorization': `Bearer ${token}` } : {};
    }

    // Make HTTP request with retry logic
    async request(url, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...this.getAuthHeaders(),
                ...options.headers
            },
            timeout: this.timeout
        };

        const requestOptions = { ...defaultOptions, ...options };

        for (let attempt = 1; attempt <= this.retryAttempts; attempt++) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), this.timeout);

                const response = await fetch(`${this.baseURL}${url}`, {
                    ...requestOptions,
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return await response.json();
                }
                return await response.text();

            } catch (error) {
                console.error(`API request attempt ${attempt} failed:`, error);

                if (attempt === this.retryAttempts) {
                    throw this.handleError(error);
                }

                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }

    // Handle API errors
    handleError(error) {
        if (error.name === 'AbortError') {
            return new Error(CONFIG.ERRORS.NETWORK);
        }

        if (error.message.includes('401')) {
            this.clearAuth();
            return new Error(CONFIG.ERRORS.UNAUTHORIZED);
        }

        if (error.message.includes('403')) {
            return new Error(CONFIG.ERRORS.FORBIDDEN);
        }

        if (error.message.includes('404')) {
            return new Error(CONFIG.ERRORS.NOT_FOUND);
        }

        if (error.message.includes('500')) {
            return new Error(CONFIG.ERRORS.SERVER_ERROR);
        }

        return new Error(CONFIG.ERRORS.NETWORK);
    }

    // Clear authentication data
    clearAuth() {
        localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
        localStorage.removeItem(CONFIG.AUTH.REFRESH_TOKEN_KEY);
        localStorage.removeItem(CONFIG.AUTH.USER_KEY);
    }

    // GET request
    async get(url, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const fullUrl = queryString ? `${url}?${queryString}` : url;
        return this.request(fullUrl, { method: 'GET' });
    }

    // POST request
    async post(url, data = {}) {
        return this.request(url, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    // PUT request
    async put(url, data = {}) {
        return this.request(url, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    // DELETE request
    async delete(url) {
        return this.request(url, { method: 'DELETE' });
    }

    // Upload file
    async upload(url, file, onProgress = null) {
        const formData = new FormData();
        formData.append('file', file);

        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            if (onProgress) {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }

            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error(`Upload failed: ${xhr.statusText}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Upload failed'));
            });

            xhr.open('POST', `${this.baseURL}${url}`);
            
            // Add auth header
            const token = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
            if (token) {
                xhr.setRequestHeader('Authorization', `Bearer ${token}`);
            }

            xhr.send(formData);
        });
    }

    // Authentication APIs
    async login(email, password) {
        return this.post('/auth/login', { email, password });
    }

    async register(userData) {
        return this.post('/auth/register', userData);
    }

    async logout() {
        return this.post('/auth/logout');
    }

    async refreshToken() {
        const refreshToken = localStorage.getItem(CONFIG.AUTH.REFRESH_TOKEN_KEY);
        return this.post('/auth/refresh', { refresh_token: refreshToken });
    }

    async getCurrentUser() {
        return this.get('/auth/me');
    }

    // Course APIs
    async getCourses(params = {}) {
        return this.get('/courses', params);
    }

    async getCourse(courseId) {
        return this.get(`/courses/${courseId}`);
    }

    async getFeaturedCourses(limit = 8) {
        return this.get('/courses/featured', { limit });
    }

    async getBestsellerCourses(limit = 8) {
        return this.get('/courses/bestsellers', { limit });
    }

    async searchCourses(query, filters = {}) {
        return this.get('/courses', { search: query, ...filters });
    }

    // Category APIs
    async getCategories() {
        return this.get('/categories');
    }

    async getCategory(categoryId) {
        return this.get(`/categories/${categoryId}`);
    }

    // User APIs
    async updateProfile(userData) {
        return this.put('/users/profile', userData);
    }

    async changePassword(passwordData) {
        return this.put('/users/change-password', passwordData);
    }

    async getUserCourses() {
        return this.get('/users/courses');
    }

    // Cart APIs
    async getCart() {
        return this.get('/cart');
    }

    async addToCart(courseId) {
        return this.post('/cart/add', { course_id: courseId });
    }

    async removeFromCart(courseId) {
        return this.delete(`/cart/remove/${courseId}`);
    }

    async clearCart() {
        return this.delete('/cart/clear');
    }

    // Wishlist APIs
    async getWishlist() {
        return this.get('/wishlist');
    }

    async addToWishlist(courseId) {
        return this.post('/wishlist/add', { course_id: courseId });
    }

    async removeFromWishlist(courseId) {
        return this.delete(`/wishlist/remove/${courseId}`);
    }

    // Order APIs
    async createOrder(orderData) {
        return this.post('/orders', orderData);
    }

    async getOrders() {
        return this.get('/orders');
    }

    async getOrder(orderId) {
        return this.get(`/orders/${orderId}`);
    }

    // Review APIs
    async getCourseReviews(courseId, params = {}) {
        return this.get(`/courses/${courseId}/reviews`, params);
    }

    async addReview(courseId, reviewData) {
        return this.post(`/courses/${courseId}/reviews`, reviewData);
    }

    async updateReview(reviewId, reviewData) {
        return this.put(`/reviews/${reviewId}`, reviewData);
    }

    async deleteReview(reviewId) {
        return this.delete(`/reviews/${reviewId}`);
    }

    // Enrollment APIs
    async enrollCourse(courseId) {
        return this.post('/enrollments', { course_id: courseId });
    }

    async getEnrollments() {
        return this.get('/enrollments');
    }

    async getCourseProgress(courseId) {
        return this.get(`/enrollments/${courseId}/progress`);
    }

    async updateLessonProgress(lessonId, progressData) {
        return this.put(`/lessons/${lessonId}/progress`, progressData);
    }
}

// Create global API instance
window.api = new ApiService();

// Auto-refresh token when needed
setInterval(async () => {
    const token = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
    if (token) {
        try {
            // Check if token is about to expire
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            
            if (payload.exp - now < CONFIG.AUTH.TOKEN_EXPIRY_BUFFER) {
                const response = await api.refreshToken();
                localStorage.setItem(CONFIG.AUTH.TOKEN_KEY, response.access_token);
                localStorage.setItem(CONFIG.AUTH.REFRESH_TOKEN_KEY, response.refresh_token);
            }
        } catch (error) {
            console.error('Token refresh failed:', error);
            api.clearAuth();
            window.location.href = CONFIG.ROUTES.LOGIN;
        }
    }
}, 60000); // Check every minute

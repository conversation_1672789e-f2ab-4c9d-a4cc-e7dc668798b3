from sqlalchemy import Column, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from ..core.database import Base


class CartItem(Base):
    __tablename__ = "cart_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    course_id = Column(UUID(as_uuid=True), ForeignKey('courses.id'), nullable=False)
    
    # Timestamps
    added_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    course = relationship("Course", foreign_keys=[course_id])
    
    def __repr__(self):
        return f"<CartItem(id={self.id}, user_id={self.user_id}, course_id={self.course_id})>"
    
    def to_dict(self, include_course=True):
        data = {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "course_id": str(self.course_id),
            "added_at": self.added_at.isoformat() if self.added_at else None
        }
        
        if include_course and self.course:
            data["course"] = {
                "id": str(self.course.id),
                "title": self.course.title,
                "slug": self.course.slug,
                "thumbnail_url": self.course.thumbnail_url,
                "price": float(self.course.price),
                "original_price": float(self.course.original_price) if self.course.original_price else None,
                "instructor_name": self.course.instructor.full_name if self.course.instructor else "",
                "rating": float(self.course.rating) if self.course.rating else 0,
                "total_reviews": self.course.total_reviews
            }
        
        return data

const redisService = require('../services/redisService');

class NotificationHandler {
  constructor(io) {
    this.io = io;
  }

  handleConnection(socket) {
    // Join user's personal notification room
    socket.on('join_notifications', async () => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required for notifications' });
          return;
        }

        const notificationRoom = `notifications_${socket.userId}`;
        await socket.join(notificationRoom);
        
        socket.emit('joined_notifications', {
          message: 'Successfully joined notifications',
          room: notificationRoom
        });
        
        console.log(`User ${socket.userId} joined notifications`);
      } catch (error) {
        console.error('Error joining notifications:', error);
        socket.emit('error', { message: 'Failed to join notifications' });
      }
    });

    // Mark notification as read
    socket.on('mark_notification_read', async (data) => {
      try {
        const { notificationId } = data;
        
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        if (!notificationId) {
          socket.emit('error', { message: 'Notification ID is required' });
          return;
        }

        // TODO: Update notification in database
        // For now, just emit confirmation
        
        socket.emit('notification_marked_read', {
          notificationId,
          message: 'Notification marked as read'
        });
        
        console.log(`Notification ${notificationId} marked as read by user ${socket.userId}`);
      } catch (error) {
        console.error('Error marking notification as read:', error);
        socket.emit('error', { message: 'Failed to mark notification as read' });
      }
    });

    // Mark all notifications as read
    socket.on('mark_all_notifications_read', async () => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        // TODO: Update all notifications in database
        
        socket.emit('all_notifications_marked_read', {
          message: 'All notifications marked as read'
        });
        
        console.log(`All notifications marked as read by user ${socket.userId}`);
      } catch (error) {
        console.error('Error marking all notifications as read:', error);
        socket.emit('error', { message: 'Failed to mark all notifications as read' });
      }
    });

    // Get unread notifications count
    socket.on('get_unread_count', async () => {
      try {
        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        // TODO: Get actual count from database
        const unreadCount = 0; // Placeholder
        
        socket.emit('unread_count', {
          count: unreadCount
        });
      } catch (error) {
        console.error('Error getting unread count:', error);
        socket.emit('error', { message: 'Failed to get unread count' });
      }
    });
  }

  // Method to send notification to specific user
  async sendNotificationToUser(userId, notification) {
    try {
      const notificationRoom = `notifications_${userId}`;
      
      // Send to all user's connected sockets
      this.io.to(notificationRoom).emit('new_notification', {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type || 'info',
        data: notification.data || {},
        timestamp: new Date().toISOString(),
        isRead: false
      });
      
      // Store in Redis for offline users
      await redisService.set(
        `notification:${userId}:${notification.id}`,
        notification,
        86400 // 24 hours
      );
      
      console.log(`Notification sent to user ${userId}`);
      return true;
    } catch (error) {
      console.error('Error sending notification to user:', error);
      return false;
    }
  }

  // Method to send notification to multiple users
  async sendNotificationToUsers(userIds, notification) {
    try {
      const promises = userIds.map(userId => 
        this.sendNotificationToUser(userId, notification)
      );
      
      await Promise.all(promises);
      console.log(`Notification sent to ${userIds.length} users`);
      return true;
    } catch (error) {
      console.error('Error sending notification to users:', error);
      return false;
    }
  }

  // Method to broadcast notification to all connected users
  async broadcastNotification(notification) {
    try {
      this.io.emit('broadcast_notification', {
        id: notification.id,
        title: notification.title,
        message: notification.message,
        type: notification.type || 'info',
        data: notification.data || {},
        timestamp: new Date().toISOString()
      });
      
      console.log('Notification broadcasted to all users');
      return true;
    } catch (error) {
      console.error('Error broadcasting notification:', error);
      return false;
    }
  }

  // Method to send course-related notifications
  async sendCourseNotification(courseId, notification, excludeUserId = null) {
    try {
      // TODO: Get all enrolled users for the course from database
      // For now, send to course room
      
      const courseRoom = `course_${courseId}`;
      
      if (excludeUserId) {
        // Send to all except the excluded user
        this.io.to(courseRoom).except(`user_${excludeUserId}`).emit('course_notification', {
          courseId,
          ...notification,
          timestamp: new Date().toISOString()
        });
      } else {
        this.io.to(courseRoom).emit('course_notification', {
          courseId,
          ...notification,
          timestamp: new Date().toISOString()
        });
      }
      
      console.log(`Course notification sent for course ${courseId}`);
      return true;
    } catch (error) {
      console.error('Error sending course notification:', error);
      return false;
    }
  }
}

module.exports = NotificationHandler;

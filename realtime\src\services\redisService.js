const redis = require('redis');
const config = require('../config');

class RedisService {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      this.client = redis.createClient({
        url: config.redisUrl,
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            console.error('Redis connection refused');
            return new Error('Redis connection refused');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            return new Error('Retry time exhausted');
          }
          if (options.attempt > 10) {
            return undefined;
          }
          return Math.min(options.attempt * 100, 3000);
        }
      });

      this.client.on('error', (err) => {
        console.error('Redis Client Error:', err);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('✅ Redis connected successfully');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        console.log('❌ Redis disconnected');
        this.isConnected = false;
      });

      await this.client.connect();
    } catch (error) {
      console.error('❌ Redis connection failed:', error);
      this.isConnected = false;
    }
  }

  async disconnect() {
    if (this.client) {
      await this.client.disconnect();
      this.isConnected = false;
    }
  }

  // User session management
  async setUserOnline(userId, socketId) {
    if (!this.isConnected) return false;
    
    try {
      const key = `user:${userId}:sessions`;
      await this.client.sAdd(key, socketId);
      await this.client.expire(key, 3600); // 1 hour expiry
      
      // Set user status
      await this.client.set(`user:${userId}:status`, 'online', { EX: 3600 });
      
      return true;
    } catch (error) {
      console.error('Error setting user online:', error);
      return false;
    }
  }

  async setUserOffline(userId, socketId) {
    if (!this.isConnected) return false;
    
    try {
      const key = `user:${userId}:sessions`;
      await this.client.sRem(key, socketId);
      
      // Check if user has other active sessions
      const activeSessions = await this.client.sCard(key);
      if (activeSessions === 0) {
        await this.client.set(`user:${userId}:status`, 'offline', { EX: 3600 });
      }
      
      return true;
    } catch (error) {
      console.error('Error setting user offline:', error);
      return false;
    }
  }

  async getUserStatus(userId) {
    if (!this.isConnected) return 'offline';
    
    try {
      const status = await this.client.get(`user:${userId}:status`);
      return status || 'offline';
    } catch (error) {
      console.error('Error getting user status:', error);
      return 'offline';
    }
  }

  async getUserSessions(userId) {
    if (!this.isConnected) return [];
    
    try {
      const sessions = await this.client.sMembers(`user:${userId}:sessions`);
      return sessions || [];
    } catch (error) {
      console.error('Error getting user sessions:', error);
      return [];
    }
  }

  // Room management
  async addUserToRoom(roomId, userId, socketId) {
    if (!this.isConnected) return false;
    
    try {
      const roomKey = `room:${roomId}:users`;
      const userKey = `user:${userId}:rooms`;
      
      await this.client.sAdd(roomKey, userId);
      await this.client.sAdd(userKey, roomId);
      await this.client.sAdd(`room:${roomId}:sockets`, socketId);
      
      // Set expiry
      await this.client.expire(roomKey, 7200); // 2 hours
      await this.client.expire(userKey, 7200);
      await this.client.expire(`room:${roomId}:sockets`, 7200);
      
      return true;
    } catch (error) {
      console.error('Error adding user to room:', error);
      return false;
    }
  }

  async removeUserFromRoom(roomId, userId, socketId) {
    if (!this.isConnected) return false;
    
    try {
      await this.client.sRem(`room:${roomId}:users`, userId);
      await this.client.sRem(`user:${userId}:rooms`, roomId);
      await this.client.sRem(`room:${roomId}:sockets`, socketId);
      
      return true;
    } catch (error) {
      console.error('Error removing user from room:', error);
      return false;
    }
  }

  async getRoomUsers(roomId) {
    if (!this.isConnected) return [];
    
    try {
      const users = await this.client.sMembers(`room:${roomId}:users`);
      return users || [];
    } catch (error) {
      console.error('Error getting room users:', error);
      return [];
    }
  }

  async getUserRooms(userId) {
    if (!this.isConnected) return [];
    
    try {
      const rooms = await this.client.sMembers(`user:${userId}:rooms`);
      return rooms || [];
    } catch (error) {
      console.error('Error getting user rooms:', error);
      return [];
    }
  }

  // Cache management
  async set(key, value, expiry = 3600) {
    if (!this.isConnected) return false;
    
    try {
      const serializedValue = typeof value === 'object' ? JSON.stringify(value) : value;
      await this.client.set(key, serializedValue, { EX: expiry });
      return true;
    } catch (error) {
      console.error('Error setting cache:', error);
      return false;
    }
  }

  async get(key) {
    if (!this.isConnected) return null;
    
    try {
      const value = await this.client.get(key);
      if (!value) return null;
      
      try {
        return JSON.parse(value);
      } catch {
        return value;
      }
    } catch (error) {
      console.error('Error getting cache:', error);
      return null;
    }
  }

  async del(key) {
    if (!this.isConnected) return false;
    
    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error('Error deleting cache:', error);
      return false;
    }
  }

  // Pub/Sub for notifications
  async publish(channel, message) {
    if (!this.isConnected) return false;
    
    try {
      const serializedMessage = typeof message === 'object' ? JSON.stringify(message) : message;
      await this.client.publish(channel, serializedMessage);
      return true;
    } catch (error) {
      console.error('Error publishing message:', error);
      return false;
    }
  }

  async subscribe(channel, callback) {
    if (!this.isConnected) return false;
    
    try {
      const subscriber = this.client.duplicate();
      await subscriber.connect();
      
      await subscriber.subscribe(channel, (message) => {
        try {
          const parsedMessage = JSON.parse(message);
          callback(parsedMessage);
        } catch {
          callback(message);
        }
      });
      
      return subscriber;
    } catch (error) {
      console.error('Error subscribing to channel:', error);
      return false;
    }
  }
}

module.exports = new RedisService();

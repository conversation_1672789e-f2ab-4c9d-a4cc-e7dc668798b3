const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');

const config = require('./src/config');
const redisService = require('./src/services/redisService');
const { authenticateSocket, optionalAuth } = require('./src/middleware/auth');
const ChatHandler = require('./src/socket/chatHandler');
const NotificationHandler = require('./src/socket/notificationHandler');
const LiveStreamHandler = require('./src/socket/liveStreamHandler');

// Create Express app
const app = express();
const server = http.createServer(app);

// Socket.io setup
const io = socketIo(server, {
  cors: {
    origin: config.allowedOrigins,
    methods: ["GET", "POST"],
    credentials: true
  },
  pingTimeout: config.socket.pingTimeout,
  pingInterval: config.socket.pingInterval
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: config.allowedOrigins,
  credentials: true
}));

if (config.isDevelopment) {
  app.use(morgan('dev'));
}

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Basic routes
app.get('/', (req, res) => {
  res.json({
    message: 'Unica Clone Realtime Server',
    version: '1.0.0',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    redis: redisService.isConnected ? 'connected' : 'disconnected',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    timestamp: new Date().toISOString()
  });
});

// Socket.io connection handling
io.use(optionalAuth); // Use optional auth for flexibility

io.on('connection', (socket) => {
  console.log(`🔌 Socket connected: ${socket.id}${socket.userId ? ` (User: ${socket.userId})` : ' (Anonymous)'}`);
  
  // Set user online status
  if (socket.userId) {
    redisService.setUserOnline(socket.userId, socket.id);
  }
  
  // Initialize handlers
  const chatHandler = new ChatHandler(io);
  const notificationHandler = new NotificationHandler(io);
  const liveStreamHandler = new LiveStreamHandler(io);
  
  // Setup event handlers
  chatHandler.handleConnection(socket);
  notificationHandler.handleConnection(socket);
  liveStreamHandler.handleConnection(socket);
  
  // Handle general events
  socket.on('ping', () => {
    socket.emit('pong', { timestamp: new Date().toISOString() });
  });
  
  socket.on('join_room', async (data) => {
    try {
      const { roomId, roomType } = data;
      
      if (!roomId || !roomType) {
        socket.emit('error', { message: 'Room ID and type are required' });
        return;
      }
      
      const fullRoomId = `${roomType}_${roomId}`;
      await socket.join(fullRoomId);
      
      if (socket.userId) {
        await redisService.addUserToRoom(fullRoomId, socket.userId, socket.id);
      }
      
      socket.emit('joined_room', {
        roomId: fullRoomId,
        message: 'Successfully joined room'
      });
      
      console.log(`Socket ${socket.id} joined room ${fullRoomId}`);
    } catch (error) {
      console.error('Error joining room:', error);
      socket.emit('error', { message: 'Failed to join room' });
    }
  });
  
  socket.on('leave_room', async (data) => {
    try {
      const { roomId, roomType } = data;
      
      if (!roomId || !roomType) {
        socket.emit('error', { message: 'Room ID and type are required' });
        return;
      }
      
      const fullRoomId = `${roomType}_${roomId}`;
      await socket.leave(fullRoomId);
      
      if (socket.userId) {
        await redisService.removeUserFromRoom(fullRoomId, socket.userId, socket.id);
      }
      
      socket.emit('left_room', {
        roomId: fullRoomId,
        message: 'Successfully left room'
      });
      
      console.log(`Socket ${socket.id} left room ${fullRoomId}`);
    } catch (error) {
      console.error('Error leaving room:', error);
      socket.emit('error', { message: 'Failed to leave room' });
    }
  });
  
  // Handle disconnect
  socket.on('disconnect', async (reason) => {
    console.log(`🔌 Socket disconnected: ${socket.id} (Reason: ${reason})`);
    
    try {
      if (socket.userId) {
        // Set user offline
        await redisService.setUserOffline(socket.userId, socket.id);
        
        // Remove from all rooms
        const userRooms = await redisService.getUserRooms(socket.userId);
        for (const roomId of userRooms) {
          await redisService.removeUserFromRoom(roomId, socket.userId, socket.id);
        }
      }
    } catch (error) {
      console.error('Error handling disconnect:', error);
    }
  });
  
  // Error handling
  socket.on('error', (error) => {
    console.error(`Socket error for ${socket.id}:`, error);
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Express error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: config.isDevelopment ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: 'The requested resource was not found'
  });
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  
  // Close server
  server.close(() => {
    console.log('✅ HTTP server closed');
  });
  
  // Disconnect Redis
  await redisService.disconnect();
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  
  server.close(() => {
    console.log('✅ HTTP server closed');
  });
  
  await redisService.disconnect();
  
  process.exit(0);
});

// Start server
async function startServer() {
  try {
    // Connect to Redis
    await redisService.connect();
    
    // Start HTTP server
    server.listen(config.port, () => {
      console.log(`🚀 Realtime server running on port ${config.port}`);
      console.log(`📝 Environment: ${config.nodeEnv}`);
      console.log(`🔧 CORS origins: ${config.allowedOrigins.join(', ')}`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

startServer();

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from ...core.database import get_db
from ...models.category import Category
from ...schemas.category import CategoryResponse, CategoryCreate, CategoryUpdate
from ...api.deps import get_current_admin, CommonQueryParams

router = APIRouter()


@router.get("/", response_model=List[CategoryResponse])
def get_categories(
    params: CommonQueryParams = Depends(),
    db: Session = Depends(get_db)
):
    """Get all categories"""
    query = db.query(Category).filter(Category.is_active == True)
    
    # Apply search
    if params.search:
        search_term = f"%{params.search}%"
        query = query.filter(Category.name.ilike(search_term))
    
    # Apply sorting
    if params.sort_by == "name":
        if params.sort_order == "asc":
            query = query.order_by(Category.name.asc())
        else:
            query = query.order_by(Category.name.desc())
    else:
        query = query.order_by(Category.sort_order.asc(), Category.name.asc())
    
    categories = query.all()
    
    return [CategoryResponse.from_orm(category) for category in categories]


@router.get("/{category_id}", response_model=CategoryResponse)
def get_category(category_id: str, db: Session = Depends(get_db)):
    """Get category by ID"""
    category = db.query(Category).filter(
        Category.id == category_id,
        Category.is_active == True
    ).first()
    
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    return CategoryResponse.from_orm(category)


@router.post("/", response_model=CategoryResponse)
def create_category(
    category_data: CategoryCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin)
):
    """Create new category (Admin only)"""
    # Check if slug already exists
    existing_category = db.query(Category).filter(Category.slug == category_data.slug).first()
    if existing_category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Category slug already exists"
        )
    
    db_category = Category(**category_data.dict())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    
    return CategoryResponse.from_orm(db_category)


@router.put("/{category_id}", response_model=CategoryResponse)
def update_category(
    category_id: str,
    category_data: CategoryUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin)
):
    """Update category (Admin only)"""
    category = db.query(Category).filter(Category.id == category_id).first()
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    # Update category
    update_data = category_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(category, field, value)
    
    db.commit()
    db.refresh(category)
    
    return CategoryResponse.from_orm(category)


@router.delete("/{category_id}")
def delete_category(
    category_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_admin)
):
    """Delete category (Admin only)"""
    category = db.query(Category).filter(Category.id == category_id).first()
    if not category:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Category not found"
        )
    
    # Soft delete by setting is_active to False
    category.is_active = False
    db.commit()
    
    return {"message": "Category deleted successfully"}

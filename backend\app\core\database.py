from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import Static<PERSON>ool
import redis
from .config import settings

# Database Engine - Support both SQLite and PostgreSQL
if settings.DATABASE_URL.startswith("sqlite"):
    engine = create_engine(
        settings.DATABASE_URL,
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
    )
else:
    # PostgreSQL
    engine = create_engine(
        settings.DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=300,
        pool_size=10,
        max_overflow=20
    )

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

# Redis connection
try:
    redis_client = redis.from_url(settings.REDIS_URL, decode_responses=True)
    # Test connection
    redis_client.ping()
    print("✅ Redis connected successfully")
except Exception as e:
    print(f"❌ Redis connection failed: {e}")
    redis_client = None


def get_db():
    """Dependency to get database session"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_redis():
    """Dependency to get Redis client"""
    return redis_client


# Database utility functions
def create_tables():
    """Create all tables"""
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """Drop all tables"""
    Base.metadata.drop_all(bind=engine)


# Cache utilities
class CacheManager:
    def __init__(self, redis_client=None):
        self.redis = redis_client or get_redis()
    
    async def get(self, key: str):
        """Get value from cache"""
        if not self.redis:
            return None
        try:
            return self.redis.get(key)
        except Exception:
            return None
    
    async def set(self, key: str, value: str, expire: int = 3600):
        """Set value in cache with expiration"""
        if not self.redis:
            return False
        try:
            return self.redis.setex(key, expire, value)
        except Exception:
            return False
    
    async def delete(self, key: str):
        """Delete key from cache"""
        if not self.redis:
            return False
        try:
            return self.redis.delete(key)
        except Exception:
            return False
    
    async def exists(self, key: str):
        """Check if key exists in cache"""
        if not self.redis:
            return False
        try:
            return self.redis.exists(key)
        except Exception:
            return False


cache_manager = CacheManager(redis_client)

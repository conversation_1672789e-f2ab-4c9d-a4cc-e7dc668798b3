# Server Configuration
PORT=3001
NODE_ENV=development

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# JWT
JWT_SECRET=your-super-secret-key-change-this-in-production

# Redis
REDIS_URL=redis://localhost:6379/1

# FastAPI Backend
BACKEND_API_URL=http://localhost:8000/api/v1

# Socket.io Configuration
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> nhập - <PERSON><PERSON></title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/assets/css/style.css" rel="stylesheet">
    
    <style>
        .auth-container {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auth-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .auth-header {
            background: var(--primary-color);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .auth-body {
            padding: 2rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .social-login {
            border-top: 1px solid #e9ecef;
            padding-top: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .btn-social {
            width: 100%;
            margin-bottom: 0.5rem;
            padding: 0.75rem;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .btn-google {
            background: #db4437;
            border-color: #db4437;
            color: white;
        }
        
        .btn-facebook {
            background: #3b5998;
            border-color: #3b5998;
            color: white;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <img src="/assets/images/logo-white.svg" alt="Unica Clone" height="40" class="mb-3">
                <h4 class="mb-0">Đăng nhập</h4>
                <p class="mb-0 opacity-75">Chào mừng bạn trở lại!</p>
            </div>
            
            <div class="auth-body">
                <form id="loginForm">
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" placeholder="Email" required>
                        <label for="email">
                            <i class="fas fa-envelope me-2"></i>Email
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" placeholder="Mật khẩu" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Mật khẩu
                        </label>
                    </div>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember">
                            <label class="form-check-label" for="remember">
                                Ghi nhớ đăng nhập
                            </label>
                        </div>
                        <a href="/forgot-password" class="text-decoration-none">
                            Quên mật khẩu?
                        </a>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-sign-in-alt me-2"></i>Đăng nhập
                    </button>
                </form>
                
                <div class="text-center mb-3">
                    <span class="text-muted">Chưa có tài khoản? </span>
                    <a href="/register" class="text-decoration-none fw-bold">
                        Đăng ký ngay
                    </a>
                </div>
                
                <div class="social-login">
                    <p class="text-center text-muted mb-3">Hoặc đăng nhập bằng</p>
                    
                    <button type="button" class="btn btn-google btn-social">
                        <i class="fab fa-google me-2"></i>Đăng nhập với Google
                    </button>
                    
                    <button type="button" class="btn btn-facebook btn-social">
                        <i class="fab fa-facebook-f me-2"></i>Đăng nhập với Facebook
                    </button>
                </div>
                
                <div class="text-center mt-3">
                    <a href="/" class="text-decoration-none">
                        <i class="fas fa-arrow-left me-2"></i>Về trang chủ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/assets/js/config.js"></script>
    <script src="/assets/js/api.js"></script>
    <script src="/assets/js/auth.js"></script>
    
    <script>
        // Demo accounts info
        document.addEventListener('DOMContentLoaded', () => {
            // Add demo accounts info
            const demoInfo = document.createElement('div');
            demoInfo.className = 'alert alert-info mt-3';
            demoInfo.innerHTML = `
                <h6><i class="fas fa-info-circle me-2"></i>Tài khoản demo:</h6>
                <small>
                    <strong>Admin:</strong> <EMAIL> / password123<br>
                    <strong>Giảng viên:</strong> <EMAIL> / password123<br>
                    <strong>Học viên:</strong> <EMAIL> / password123
                </small>
            `;
            
            document.querySelector('.auth-body').appendChild(demoInfo);
            
            // Quick login buttons
            const quickLogin = document.createElement('div');
            quickLogin.className = 'mt-3';
            quickLogin.innerHTML = `
                <p class="text-center text-muted mb-2">Đăng nhập nhanh:</p>
                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm flex-fill" onclick="quickLogin('admin')">
                        Admin
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm flex-fill" onclick="quickLogin('instructor')">
                        Giảng viên
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm flex-fill" onclick="quickLogin('student')">
                        Học viên
                    </button>
                </div>
            `;
            
            document.querySelector('.auth-body').appendChild(quickLogin);
        });
        
        // Quick login function
        window.quickLogin = function(type) {
            const accounts = {
                admin: { email: '<EMAIL>', password: 'password123' },
                instructor: { email: '<EMAIL>', password: 'password123' },
                student: { email: '<EMAIL>', password: 'password123' }
            };
            
            const account = accounts[type];
            if (account) {
                document.getElementById('email').value = account.email;
                document.getElementById('password').value = account.password;
            }
        };
        
        // Check if already logged in
        if (auth.isAuthenticated()) {
            const urlParams = new URLSearchParams(window.location.search);
            const redirectUrl = urlParams.get('redirect') || '/';
            window.location.href = redirectUrl;
        }
    </script>
</body>
</html>

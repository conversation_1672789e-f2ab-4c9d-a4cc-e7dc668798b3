/* Un<PERSON>lone - Custom Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
}

/* Header Styles */
.header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: white;
    border-bottom: 1px solid #e9ecef;
}

.navbar-brand img {
    transition: var(--transition);
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.categories-menu {
    min-width: 250px;
    max-height: 400px;
    overflow-y: auto;
}

.categories-menu .dropdown-item {
    padding: 12px 20px;
    border-bottom: 1px solid #f8f9fa;
    transition: var(--transition);
}

.categories-menu .dropdown-item:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.categories-menu .dropdown-item i {
    width: 20px;
    color: var(--primary-color);
}

.categories-menu .dropdown-item:hover i {
    color: white;
}

/* Search Styles */
.input-group .form-control {
    border-right: none;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .btn {
    border-left: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 500px;
    display: flex;
    align-items: center;
}

.hero-section img {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Stats Section */
.stats-section {
    border-bottom: 1px solid #e9ecef;
}

.stat-item {
    padding: 20px;
    transition: var(--transition);
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-item h3 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

/* Course Card Styles */
.course-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
    height: 100%;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.course-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition);
}

.course-card:hover img {
    transform: scale(1.05);
}

.course-card-body {
    padding: 20px;
}

.course-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--dark-color);
    text-decoration: none;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.course-title:hover {
    color: var(--primary-color);
}

.course-instructor {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.course-rating {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 10px;
}

.rating-stars {
    color: #ffc107;
}

.rating-text {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

.course-price {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--danger-color);
}

.original-price {
    font-size: 1rem;
    color: var(--secondary-color);
    text-decoration: line-through;
}

.discount-badge {
    background: var(--danger-color);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
}

.course-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.course-badges {
    display: flex;
    gap: 5px;
    margin-bottom: 10px;
}

.badge-bestseller {
    background: var(--warning-color);
    color: var(--dark-color);
}

.badge-featured {
    background: var(--success-color);
    color: white;
}

/* Category Card Styles */
.category-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px 20px;
    text-align: center;
    transition: var(--transition);
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    color: inherit;
}

.category-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.category-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.category-count {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 10px 20px;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-primary {
    background: var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background: #0056b3;
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.4);
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* Loading Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 3rem 0;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .stats-section .row {
        text-align: center;
    }
    
    .stat-item {
        margin-bottom: 1rem;
    }
    
    .course-card {
        margin-bottom: 2rem;
    }
    
    .category-card {
        margin-bottom: 1.5rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.shadow-hover {
    transition: var(--transition);
}

.shadow-hover:hover {
    box-shadow: var(--box-shadow);
}

/* Footer Styles */
.footer {
    background: var(--dark-color) !important;
}

.footer a {
    transition: var(--transition);
}

.footer a:hover {
    color: var(--primary-color) !important;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    transition: var(--transition);
}

.social-links a:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
}

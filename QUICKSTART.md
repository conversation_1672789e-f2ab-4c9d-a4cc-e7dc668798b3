# 🚀 Hướng dẫn chạy nhanh Unica Clone

## ⚡ Cách nhanh nhất (3 bước)

### 1️⃣ Kiểm tra yêu cầu hệ thống
```bash
python check_requirements.py
```

### 2️⃣ Setup tự động
```bash
python setup.py
```

### 3️⃣ Chạy dự án
```bash
# Cách 1: Script tự động
python run.py

# Cách 2: Script khởi động
# Windows
start.bat

# macOS/Linux
./start.sh
```

## 📋 Yêu cầu tối thiểu

- **Python 3.8+** ✅
- **Node.js 16+** ✅  
- **PostgreSQL 12+** ✅
- **Redis 6+** ✅ (tùy chọn)

## 🔧 Cài đặt phần mềm

### Windows:
```powershell
# Python - Tải từ python.org
# Node.js - Tải từ nodejs.org  
# PostgreSQL - Tải từ postgresql.org
# Redis - Tải từ redis.io hoặc dùng WSL
```

### macOS:
```bash
# Cài Homebrew trước
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Cài các phần mềm
brew install python node postgresql redis
brew services start postgresql
brew services start redis
```

### Ubuntu/Debian:
```bash
# Cập nhật package list
sudo apt update

# Cài Python
sudo apt install python3 python3-pip python3-venv

# Cài Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs

# Cài PostgreSQL
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Cài Redis
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## 🗄️ Thiết lập Database

### Tạo database PostgreSQL:
```bash
# Đăng nhập PostgreSQL
sudo -u postgres psql

# Tạo database và user
CREATE DATABASE unica_clone;
CREATE USER unica_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE unica_clone TO unica_user;
\q

# Import schema
psql -U unica_user -d unica_clone -f database/schema.sql
psql -U unica_user -d unica_clone -f database/sample_data.sql
```

### Cấu hình .env:
```bash
# Chỉnh sửa backend/.env
DATABASE_URL=postgresql://unica_user:your_password@localhost:5432/unica_clone
```

## 🌐 Truy cập ứng dụng

Sau khi chạy thành công:

- **🏠 Trang chủ**: http://localhost:3000
- **🔧 API Backend**: http://localhost:8000  
- **📚 API Docs**: http://localhost:8000/docs
- **📡 Realtime**: http://localhost:3001

## 👥 Tài khoản demo

| Loại | Email | Password |
|------|-------|----------|
| **Admin** | <EMAIL> | password123 |
| **Giảng viên** | <EMAIL> | password123 |
| **Học viên** | <EMAIL> | password123 |

## 🐛 Xử lý lỗi thường gặp

### ❌ Lỗi "Port already in use"
```bash
# Tìm process đang dùng port
# Windows
netstat -ano | findstr :8000

# macOS/Linux  
lsof -i :8000

# Kill process
kill -9 <PID>
```

### ❌ Lỗi PostgreSQL connection
```bash
# Kiểm tra PostgreSQL đang chạy
sudo systemctl status postgresql

# Restart PostgreSQL
sudo systemctl restart postgresql

# Kiểm tra kết nối
psql -U unica_user -d unica_clone -h localhost
```

### ❌ Lỗi Python dependencies
```bash
# Cập nhật pip
python -m pip install --upgrade pip

# Cài đặt lại
cd backend
pip install -r requirements.txt --force-reinstall
```

### ❌ Lỗi Node.js dependencies
```bash
# Xóa và cài lại
cd realtime
rm -rf node_modules package-lock.json
npm install
```

## 📱 Chạy từng service riêng

### Backend (Terminal 1):
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### Realtime (Terminal 2):
```bash
cd realtime
npm install
npm run dev
```

### Frontend (Terminal 3):
```bash
cd frontend
python -m http.server 3000
```

## 🔍 Kiểm tra hoạt động

### Test API:
```bash
# Kiểm tra health
curl http://localhost:8000/health

# Lấy danh sách khóa học
curl http://localhost:8000/api/v1/courses
```

### Test Realtime:
- Mở http://localhost:3001 trong browser
- Kiểm tra console có thông báo kết nối

### Test Frontend:
- Mở http://localhost:3000
- Đăng nhập với tài khoản demo
- Kiểm tra các tính năng cơ bản

## 🎯 Tính năng chính

✅ **Authentication** - Đăng ký/Đăng nhập  
✅ **Courses** - Quản lý khóa học  
✅ **E-commerce** - Giỏ hàng, thanh toán  
✅ **Realtime** - Chat, notifications  
✅ **Live Streaming** - Lớp học trực tuyến  
✅ **Admin Panel** - Quản trị hệ thống  

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs trong terminal
2. Xem file SETUP.md chi tiết hơn
3. Chạy `python check_requirements.py` để kiểm tra
4. Tạo issue trên GitHub

---

**🎉 Chúc bạn chạy dự án thành công!**

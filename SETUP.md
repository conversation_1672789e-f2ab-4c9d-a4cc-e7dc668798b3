# Hướng dẫn cài đặt và chạy Unica Clone

## <PERSON><PERSON><PERSON> c<PERSON>u hệ thống

### <PERSON><PERSON><PERSON> mềm cần thiết:
- **Python 3.8+** - Cho backend FastAPI
- **Node.js 16+** - Cho realtime server
- **PostgreSQL 12+** - Database chính
- **Redis 6+** - Cache và session storage

### Hệ điều hành hỗ trợ:
- Windows 10/11
- macOS 10.15+
- Ubuntu 18.04+

## Cài đặt các phần mềm cần thiết

### 1. Python
```bash
# Windows - Tải từ python.org
# macOS
brew install python

# Ubuntu
sudo apt update
sudo apt install python3 python3-pip
```

### 2. Node.js
```bash
# Windows - Tải từ nodejs.org
# macOS
brew install node

# Ubuntu
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 3. PostgreSQL
```bash
# Windows - <PERSON><PERSON><PERSON> từ postgresql.org
# macOS
brew install postgresql
brew services start postgresql

# Ubuntu
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### 4. Redis
```bash
# Windows - Tải từ redis.io hoặc dùng WSL
# macOS
brew install redis
brew services start redis

# Ubuntu
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

## Thiết lập Database

### 1. Tạo database PostgreSQL
```bash
# Đăng nhập PostgreSQL
sudo -u postgres psql

# Tạo database và user
CREATE DATABASE unica_clone;
CREATE USER unica_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE unica_clone TO unica_user;
\q
```

### 2. Import schema
```bash
psql -U unica_user -d unica_clone -f database/schema.sql
psql -U unica_user -d unica_clone -f database/sample_data.sql
```

## Cài đặt dự án

### 1. Clone repository
```bash
git clone <repository-url>
cd unica-clone
```

### 2. Thiết lập Backend (FastAPI)
```bash
cd backend

# Tạo virtual environment
python -m venv venv

# Kích hoạt virtual environment
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# Cài đặt dependencies
pip install -r requirements.txt

# Tạo file .env
cp .env.example .env
# Chỉnh sửa .env với thông tin database của bạn
```

### 3. Thiết lập Realtime Server (Node.js)
```bash
cd realtime

# Cài đặt dependencies
npm install

# Tạo file .env
cp .env.example .env
# Chỉnh sửa .env nếu cần
```

### 4. Thiết lập Frontend
```bash
cd frontend
# Không cần cài đặt gì thêm, chỉ cần web server
```

## Chạy dự án

### Cách 1: Chạy tự động (Khuyến nghị)
```bash
# Từ thư mục gốc của dự án
python run.py
```

Script này sẽ:
- Kiểm tra các yêu cầu hệ thống
- Tự động cài đặt dependencies
- Khởi động tất cả services
- Hiển thị URLs để truy cập

### Cách 2: Chạy thủ công

#### Terminal 1 - Backend
```bash
cd backend
source venv/bin/activate  # Windows: venv\Scripts\activate
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

#### Terminal 2 - Realtime Server
```bash
cd realtime
npm run dev
```

#### Terminal 3 - Frontend
```bash
cd frontend
python -m http.server 3000
```

## Truy cập ứng dụng

Sau khi chạy thành công:

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Realtime Server**: http://localhost:3001

## Tài khoản mặc định

### Admin
- Email: `<EMAIL>`
- Password: `password123`

### Instructor
- Email: `<EMAIL>`
- Password: `password123`

### Student
- Email: `<EMAIL>`
- Password: `password123`

## Cấu hình môi trường

### Backend (.env)
```env
DATABASE_URL=postgresql://unica_user:your_password@localhost:5432/unica_clone
REDIS_URL=redis://localhost:6379/0
SECRET_KEY=your-super-secret-key-change-this-in-production
```

### Realtime (.env)
```env
PORT=3001
REDIS_URL=redis://localhost:6379/1
JWT_SECRET=your-super-secret-key-change-this-in-production
BACKEND_API_URL=http://localhost:8000/api/v1
```

## Troubleshooting

### Lỗi kết nối Database
```bash
# Kiểm tra PostgreSQL đang chạy
sudo systemctl status postgresql

# Kiểm tra kết nối
psql -U unica_user -d unica_clone -h localhost
```

### Lỗi kết nối Redis
```bash
# Kiểm tra Redis đang chạy
redis-cli ping

# Nếu trả về PONG thì Redis đang hoạt động
```

### Lỗi Port đã được sử dụng
```bash
# Tìm process đang sử dụng port
# Windows
netstat -ano | findstr :8000

# macOS/Linux
lsof -i :8000

# Kill process
kill -9 <PID>
```

### Lỗi Python dependencies
```bash
# Cập nhật pip
python -m pip install --upgrade pip

# Cài đặt lại dependencies
pip install -r requirements.txt --force-reinstall
```

### Lỗi Node.js dependencies
```bash
# Xóa node_modules và cài đặt lại
rm -rf node_modules package-lock.json
npm install
```

## Development Tips

### 1. Hot Reload
- Backend: FastAPI tự động reload khi có thay đổi
- Realtime: Nodemon tự động restart server
- Frontend: Refresh browser để thấy thay đổi

### 2. Debugging
- Backend: Sử dụng debugger của IDE hoặc `print()`
- Realtime: Sử dụng `console.log()`
- Frontend: Sử dụng Browser DevTools

### 3. Database Migration
```bash
cd backend
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

### 4. Testing
```bash
# Backend tests
cd backend
pytest

# Realtime tests
cd realtime
npm test
```

## Production Deployment

Để deploy production, cần:

1. Thay đổi các secret keys
2. Sử dụng production database
3. Cấu hình reverse proxy (Nginx)
4. Sử dụng process manager (PM2, Supervisor)
5. Cấu hình SSL/HTTPS
6. Thiết lập monitoring và logging

Xem file `DEPLOYMENT.md` để biết chi tiết.

## Hỗ trợ

Nếu gặp vấn đề:

1. Kiểm tra logs trong terminal
2. Xem file `TROUBLESHOOTING.md`
3. Tạo issue trên GitHub
4. Liên hệ team phát triển

## Cấu trúc dự án

```
unica-clone/
├── backend/          # FastAPI backend
├── realtime/         # Node.js realtime server
├── frontend/         # Web frontend
├── database/         # Database scripts
├── docs/            # Documentation
├── run.py           # Development runner
└── README.md        # Project overview
```

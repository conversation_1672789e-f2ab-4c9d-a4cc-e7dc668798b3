#!/usr/bin/env python3
"""
Unica Clone - Development Runner
<PERSON><PERSON><PERSON><PERSON> các services cần thiết cho development
"""

import subprocess
import sys
import os
import time
import signal
import threading
from pathlib import Path

class ServiceRunner:
    def __init__(self):
        self.processes = []
        self.running = True
        
    def run_command(self, command, cwd, name):
        """Chạy command trong directory cụ thể"""
        print(f"🚀 Starting {name}...")
        try:
            process = subprocess.Popen(
                command,
                cwd=cwd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.processes.append((process, name))
            
            # Log output in separate thread
            def log_output():
                for line in iter(process.stdout.readline, ''):
                    if self.running:
                        print(f"[{name}] {line.strip()}")
                        
            thread = threading.Thread(target=log_output)
            thread.daemon = True
            thread.start()
            
            return process
            
        except Exception as e:
            print(f"❌ Failed to start {name}: {e}")
            return None
    
    def check_requirements(self):
        """Kiểm tra các yêu cầu cần thiết"""
        print("🔍 Checking requirements...")
        
        # Check Python
        try:
            python_version = subprocess.check_output([sys.executable, "--version"], universal_newlines=True)
            print(f"✅ Python: {python_version.strip()}")
        except:
            print("❌ Python not found")
            return False
        
        # Check Node.js
        try:
            node_version = subprocess.check_output(["node", "--version"], universal_newlines=True)
            print(f"✅ Node.js: {node_version.strip()}")
        except:
            print("❌ Node.js not found. Please install Node.js")
            return False
        
        # Check PostgreSQL
        try:
            pg_version = subprocess.check_output(["psql", "--version"], universal_newlines=True)
            print(f"✅ PostgreSQL: {pg_version.strip()}")
        except:
            print("⚠️  PostgreSQL not found. Make sure PostgreSQL is installed and running")
        
        # Check Redis
        try:
            redis_version = subprocess.check_output(["redis-cli", "--version"], universal_newlines=True)
            print(f"✅ Redis: {redis_version.strip()}")
        except:
            print("⚠️  Redis not found. Make sure Redis is installed and running")
        
        return True
    
    def setup_environment(self):
        """Thiết lập môi trường development"""
        print("🔧 Setting up environment...")
        
        # Create .env files if they don't exist
        backend_env = Path("backend/.env")
        if not backend_env.exists():
            print("📝 Creating backend/.env from example...")
            subprocess.run(["cp", "backend/.env.example", "backend/.env"])
        
        realtime_env = Path("realtime/.env")
        if not realtime_env.exists():
            print("📝 Creating realtime/.env from example...")
            subprocess.run(["cp", "realtime/.env.example", "realtime/.env"])
        
        # Install Python dependencies
        if Path("backend/requirements.txt").exists():
            print("📦 Installing Python dependencies...")
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                         cwd="backend")
        
        # Install Node.js dependencies
        if Path("realtime/package.json").exists():
            print("📦 Installing Node.js dependencies...")
            subprocess.run(["npm", "install"], cwd="realtime")
    
    def start_services(self):
        """Khởi động tất cả services"""
        print("🚀 Starting all services...")
        
        # Start FastAPI backend
        backend_process = self.run_command(
            f"{sys.executable} -m uvicorn main:app --reload --host 0.0.0.0 --port 8000",
            "backend",
            "FastAPI Backend"
        )
        
        # Wait a bit for backend to start
        time.sleep(3)
        
        # Start Node.js realtime server
        realtime_process = self.run_command(
            "npm run dev",
            "realtime", 
            "Realtime Server"
        )
        
        # Wait a bit for realtime server to start
        time.sleep(2)
        
        # Start frontend server (simple HTTP server)
        frontend_process = self.run_command(
            f"{sys.executable} -m http.server 3000",
            "frontend",
            "Frontend Server"
        )
        
        print("\n" + "="*60)
        print("🎉 All services started successfully!")
        print("="*60)
        print("📱 Frontend:      http://localhost:3000")
        print("🔧 Backend API:   http://localhost:8000")
        print("📡 Realtime:      http://localhost:3001")
        print("📚 API Docs:      http://localhost:8000/docs")
        print("="*60)
        print("Press Ctrl+C to stop all services")
        print("="*60)
        
        return True
    
    def stop_services(self):
        """Dừng tất cả services"""
        print("\n🛑 Stopping all services...")
        self.running = False
        
        for process, name in self.processes:
            try:
                print(f"⏹️  Stopping {name}...")
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"🔪 Force killing {name}...")
                process.kill()
            except Exception as e:
                print(f"❌ Error stopping {name}: {e}")
        
        print("✅ All services stopped")
    
    def run(self):
        """Chạy toàn bộ development environment"""
        try:
            print("🌟 Unica Clone - Development Environment")
            print("="*50)
            
            if not self.check_requirements():
                return False
            
            self.setup_environment()
            
            if not self.start_services():
                return False
            
            # Keep running until interrupted
            try:
                while self.running:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
        finally:
            self.stop_services()
        
        return True

def main():
    """Main function"""
    runner = ServiceRunner()
    
    # Handle Ctrl+C gracefully
    def signal_handler(sig, frame):
        runner.stop_services()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    success = runner.run()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

from sqlalchemy import Column, <PERSON>, Boolean, DateTime, DECIMAL, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
import uuid
from ..core.database import Base


class Coupon(Base):
    __tablename__ = "coupons"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(50), unique=True, nullable=False)
    description = Column(String(255))
    discount_type = Column(String(20), default='percentage')  # percentage, fixed
    discount_value = Column(DECIMAL(10, 2), nullable=False)
    min_amount = Column(DECIMAL(10, 2), default=0)
    max_uses = Column(Integer)
    used_count = Column(Integer, default=0)
    valid_from = Column(DateTime(timezone=True))
    valid_until = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def __repr__(self):
        return f"<Coupon(id={self.id}, code={self.code}, discount_value={self.discount_value})>"
    
    def to_dict(self):
        return {
            "id": str(self.id),
            "code": self.code,
            "description": self.description,
            "discount_type": self.discount_type,
            "discount_value": float(self.discount_value),
            "min_amount": float(self.min_amount),
            "max_uses": self.max_uses,
            "used_count": self.used_count,
            "valid_from": self.valid_from.isoformat() if self.valid_from else None,
            "valid_until": self.valid_until.isoformat() if self.valid_until else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
    
    def is_valid(self, amount=None):
        """Check if coupon is valid"""
        if not self.is_active:
            return False
        
        # Check usage limit
        if self.max_uses and self.used_count >= self.max_uses:
            return False
        
        # Check date range
        now = func.now()
        if self.valid_from and now < self.valid_from:
            return False
        if self.valid_until and now > self.valid_until:
            return False
        
        # Check minimum amount
        if amount and self.min_amount and amount < self.min_amount:
            return False
        
        return True
    
    def calculate_discount(self, amount):
        """Calculate discount amount"""
        if not self.is_valid(amount):
            return 0
        
        if self.discount_type == 'percentage':
            return amount * (float(self.discount_value) / 100)
        else:  # fixed
            return min(float(self.discount_value), amount)

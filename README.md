# Unica Clone - <PERSON><PERSON><PERSON> tảng học trực tuyến

## <PERSON><PERSON> tả dự án
Dự án clone trang unica.vn - một nền tảng học trực tuyến toàn diện với các tính năng:
- <PERSON>ệ thống khóa học đa dạng
- Livestream/Video học trực tuyến  
- Quản lý giảng viên và học viên
- <PERSON><PERSON> toán và giỏ hàng
- Chat và thông báo realtime
- Đánh giá và nhận xét

## Kiến trúc hệ thống
- **Backend API**: FastAPI (Python)
- **Realtime Server**: Node.js + Socket.io
- **Database**: PostgreSQL
- **Frontend**: HTML/CSS/JavaScript (Vanilla)

## Cấu trúc thư mục
```
unica-clone/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core settings
│   │   ├── models/         # Database models
│   │   ├── schemas/        # Pydantic schemas
│   │   └── services/       # Business logic
│   ├── requirements.txt
│   └── main.py
├── realtime/               # Node.js realtime server
│   ├── src/
│   │   ├── controllers/
│   │   ├── services/
│   │   └── socket/
│   ├── package.json
│   └── server.js
├── frontend/               # Web frontend
│   ├── assets/
│   │   ├── css/
│   │   ├── js/
│   │   └── images/
│   ├── pages/
│   └── index.html
├── database/               # Database scripts
│   ├── migrations/
│   └── schema.sql
└── docs/                   # Documentation
```

## Cài đặt và chạy

### 1. Database (PostgreSQL)
```bash
# Tạo database
createdb unica_clone

# Import schema
psql unica_clone < database/schema.sql
```

### 2. Backend (FastAPI)
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload --port 8000
```

### 3. Realtime Server (Node.js)
```bash
cd realtime
npm install
npm start
```

### 4. Frontend
```bash
cd frontend
# Mở index.html trong browser hoặc dùng live server
python -m http.server 3000
```

## API Endpoints
- `GET /api/courses` - Danh sách khóa học
- `POST /api/auth/login` - Đăng nhập
- `POST /api/auth/register` - Đăng ký
- `GET /api/users/profile` - Thông tin user
- `POST /api/orders` - Tạo đơn hàng
- `GET /api/categories` - Danh mục khóa học

## Realtime Features
- Chat trong khóa học
- Thông báo realtime
- Live streaming
- Online users tracking

## Tính năng chính
1. **Hệ thống Authentication**
   - Đăng ký/Đăng nhập
   - JWT tokens
   - Role-based access

2. **Quản lý khóa học**
   - CRUD khóa học
   - Categories và tags
   - Video lessons
   - Progress tracking

3. **E-commerce**
   - Giỏ hàng
   - Thanh toán
   - Đơn hàng
   - Coupons/Discounts

4. **Tương tác**
   - Rating & Reviews
   - Comments
   - Q&A
   - Live chat

5. **Admin Panel**
   - Quản lý users
   - Quản lý courses
   - Analytics
   - Reports

## Công nghệ sử dụng
- **Backend**: FastAPI, SQLAlchemy, Alembic
- **Realtime**: Node.js, Socket.io, Express
- **Database**: PostgreSQL, Redis (cache)
- **Frontend**: Vanilla JS, Bootstrap, Chart.js
- **Authentication**: JWT, bcrypt
- **File Storage**: Local storage / AWS S3

// Authentication Service for Unica Clone

class AuthService {
    constructor() {
        this.tokenKey = CONFIG.AUTH.TOKEN_KEY;
        this.refreshTokenKey = CONFIG.AUTH.REFRESH_TOKEN_KEY;
        this.userKey = CONFIG.AUTH.USER_KEY;
    }

    // Check if user is authenticated
    isAuthenticated() {
        const token = localStorage.getItem(this.tokenKey);
        if (!token) return false;

        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            return payload.exp > now;
        } catch (error) {
            return false;
        }
    }

    // Get current user
    getCurrentUser() {
        const userStr = localStorage.getItem(this.userKey);
        return userStr ? JSON.parse(userStr) : null;
    }

    // Get access token
    getAccessToken() {
        return localStorage.getItem(this.tokenKey);
    }

    // Get refresh token
    getRefreshToken() {
        return localStorage.getItem(this.refreshTokenKey);
    }

    // Store authentication data
    storeAuthData(tokenResponse) {
        localStorage.setItem(this.tokenKey, tokenResponse.access_token);
        localStorage.setItem(this.refreshTokenKey, tokenResponse.refresh_token);
        localStorage.setItem(this.userKey, JSON.stringify(tokenResponse.user));
    }

    // Clear authentication data
    clearAuthData() {
        localStorage.removeItem(this.tokenKey);
        localStorage.removeItem(this.refreshTokenKey);
        localStorage.removeItem(this.userKey);
    }

    // Login
    async login(email, password) {
        try {
            const response = await api.login(email, password);
            this.storeAuthData(response);
            return response;
        } catch (error) {
            throw error;
        }
    }

    // Register
    async register(userData) {
        try {
            const response = await api.register(userData);
            this.storeAuthData(response);
            return response;
        } catch (error) {
            throw error;
        }
    }

    // Logout
    async logout() {
        try {
            await api.logout();
        } catch (error) {
            console.error('Logout API error:', error);
        } finally {
            this.clearAuthData();
            window.location.href = '/';
        }
    }

    // Refresh token
    async refreshToken() {
        try {
            const refreshToken = this.getRefreshToken();
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await api.refreshToken();
            this.storeAuthData(response);
            return response;
        } catch (error) {
            this.clearAuthData();
            throw error;
        }
    }

    // Check if user has specific role
    hasRole(role) {
        const user = this.getCurrentUser();
        return user && user.role === role;
    }

    // Check if user is admin
    isAdmin() {
        return this.hasRole('admin');
    }

    // Check if user is instructor
    isInstructor() {
        return this.hasRole('instructor') || this.isAdmin();
    }

    // Check if user is student
    isStudent() {
        return this.hasRole('student');
    }

    // Redirect to login if not authenticated
    requireAuth(redirectUrl = null) {
        if (!this.isAuthenticated()) {
            const currentUrl = redirectUrl || window.location.pathname + window.location.search;
            window.location.href = `/login?redirect=${encodeURIComponent(currentUrl)}`;
            return false;
        }
        return true;
    }

    // Redirect to login if not instructor
    requireInstructor() {
        if (!this.requireAuth()) return false;
        
        if (!this.isInstructor()) {
            window.app.showToast('Bạn cần quyền giảng viên để truy cập trang này', 'error');
            window.location.href = '/';
            return false;
        }
        return true;
    }

    // Redirect to login if not admin
    requireAdmin() {
        if (!this.requireAuth()) return false;
        
        if (!this.isAdmin()) {
            window.app.showToast('Bạn cần quyền quản trị để truy cập trang này', 'error');
            window.location.href = '/';
            return false;
        }
        return true;
    }
}

// Create global auth instance
window.auth = new AuthService();

// Auto-refresh token before expiry
setInterval(async () => {
    if (auth.isAuthenticated()) {
        const token = auth.getAccessToken();
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            
            // Refresh if token expires in next 5 minutes
            if (payload.exp - now < CONFIG.AUTH.TOKEN_EXPIRY_BUFFER) {
                await auth.refreshToken();
                console.log('Token refreshed automatically');
            }
        } catch (error) {
            console.error('Auto token refresh failed:', error);
            auth.clearAuthData();
            window.location.href = '/login';
        }
    }
}, 60000); // Check every minute

// Handle login form
document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const submitBtn = loginForm.querySelector('button[type="submit"]');
            
            try {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng nhập...';
                
                await auth.login(email, password);
                
                window.app.showToast(CONFIG.SUCCESS.LOGIN, 'success');
                
                // Redirect to intended page or home
                const urlParams = new URLSearchParams(window.location.search);
                const redirectUrl = urlParams.get('redirect') || '/';
                window.location.href = redirectUrl;
                
            } catch (error) {
                window.app.showToast(error.message, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Đăng nhập';
            }
        });
    }

    // Handle register form
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData(registerForm);
            const userData = {
                email: formData.get('email'),
                password: formData.get('password'),
                full_name: formData.get('full_name'),
                phone: formData.get('phone'),
                role: formData.get('role') || 'student'
            };
            
            const submitBtn = registerForm.querySelector('button[type="submit"]');
            
            try {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng ký...';
                
                await auth.register(userData);
                
                window.app.showToast(CONFIG.SUCCESS.REGISTER, 'success');
                window.location.href = '/';
                
            } catch (error) {
                window.app.showToast(error.message, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = 'Đăng ký';
            }
        });
    }
});

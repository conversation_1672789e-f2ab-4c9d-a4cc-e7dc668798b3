from fastapi import APIRouter
from .auth import router as auth_router
from .courses import router as courses_router
from .categories import router as categories_router
from .users import router as users_router

api_router = APIRouter()

# Include all routers
api_router.include_router(auth_router, prefix="/auth", tags=["Authentication"])
api_router.include_router(courses_router, prefix="/courses", tags=["Courses"])
api_router.include_router(categories_router, prefix="/categories", tags=["Categories"])
api_router.include_router(users_router, prefix="/users", tags=["Users"])

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/unica_clone
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=unica_clone
DATABASE_USER=username
DATABASE_PASSWORD=password

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_EXTENSIONS=["jpg", "jpeg", "png", "gif", "mp4", "pdf"]

# Email
SENDGRID_API_KEY=your-sendgrid-api-key
FROM_EMAIL=<EMAIL>

# Payment
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# AWS S3 (Optional)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=unica-clone-uploads

# Environment
ENVIRONMENT=development
DEBUG=True

# API
API_V1_STR=/api/v1
PROJECT_NAME=Unica Clone
VERSION=1.0.0

# Realtime Server
REALTIME_SERVER_URL=http://localhost:3001

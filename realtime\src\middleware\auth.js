const jwt = require('jsonwebtoken');
const config = require('../config');

/**
 * Middleware to authenticate socket connections using JWT
 */
const authenticateSocket = (socket, next) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return next(new Error('Authentication token required'));
    }
    
    // Verify JWT token
    const decoded = jwt.verify(token, config.jwtSecret);
    
    // Check token type
    if (decoded.type !== 'access') {
      return next(new Error('Invalid token type'));
    }
    
    // Check expiration
    if (decoded.exp && Date.now() >= decoded.exp * 1000) {
      return next(new Error('Token expired'));
    }
    
    // Attach user info to socket
    socket.userId = decoded.sub;
    socket.userEmail = decoded.email;
    socket.userRole = decoded.role;
    
    next();
  } catch (error) {
    console.error('Socket authentication error:', error.message);
    next(new Error('Invalid authentication token'));
  }
};

/**
 * Optional authentication - doesn't reject connection if no token
 */
const optionalAuth = (socket, next) => {
  try {
    const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
    
    if (token) {
      const decoded = jwt.verify(token, config.jwtSecret);
      
      if (decoded.type === 'access' && decoded.exp && Date.now() < decoded.exp * 1000) {
        socket.userId = decoded.sub;
        socket.userEmail = decoded.email;
        socket.userRole = decoded.role;
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

/**
 * Check if user has instructor role
 */
const requireInstructor = (socket, next) => {
  if (!socket.userId) {
    return next(new Error('Authentication required'));
  }
  
  if (socket.userRole !== 'instructor' && socket.userRole !== 'admin') {
    return next(new Error('Instructor role required'));
  }
  
  next();
};

/**
 * Check if user has admin role
 */
const requireAdmin = (socket, next) => {
  if (!socket.userId) {
    return next(new Error('Authentication required'));
  }
  
  if (socket.userRole !== 'admin') {
    return next(new Error('Admin role required'));
  }
  
  next();
};

module.exports = {
  authenticateSocket,
  optionalAuth,
  requireInstructor,
  requireAdmin
};

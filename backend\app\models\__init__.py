# Import all models here for Alembic to detect them
from .user import User
from .category import Category
from .tag import Tag
from .course import Course
from .section import Section
from .lesson import Lesson
from .enrollment import Enrollment
from .lesson_progress import LessonProgress
from .review import Review
from .order import Order, OrderItem
from .coupon import Coupon
from .cart import CartItem
from .wishlist import WishlistItem
from .notification import Notification
from .live_session import LiveSession, LiveSessionParticipant
from .comment import Comment

__all__ = [
    "User",
    "Category",
    "Tag",
    "Course",
    "Section",
    "Lesson",
    "Enrollment",
    "LessonProgress",
    "Review",
    "Order",
    "OrderItem",
    "Coupon",
    "CartItem",
    "WishlistItem",
    "Notification",
    "LiveSession",
    "LiveSessionParticipant",
    "Comment"
]

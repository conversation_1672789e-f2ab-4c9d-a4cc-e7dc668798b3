// Main JavaScript for Unica Clone

class UnicaApp {
    constructor() {
        this.init();
    }

    async init() {
        // Initialize app
        this.setupEventListeners();
        this.checkAuthentication();
        await this.loadInitialData();
        this.initializeComponents();
    }

    setupEventListeners() {
        // Search functionality
        const searchForm = document.querySelector('form[role="search"]');
        if (searchForm) {
            searchForm.addEventListener('submit', this.handleSearch.bind(this));
        }

        // Cart functionality
        document.addEventListener('click', (e) => {
            if (e.target.matches('.add-to-cart')) {
                this.handleAddToCart(e);
            }
            if (e.target.matches('.add-to-wishlist')) {
                this.handleAddToWishlist(e);
            }
        });

        // Navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('a[href^="/"]')) {
                e.preventDefault();
                this.navigate(e.target.getAttribute('href'));
            }
        });
    }

    checkAuthentication() {
        const token = localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
        const user = localStorage.getItem(CONFIG.AUTH.USER_KEY);

        if (token && user) {
            this.updateUIForAuthenticatedUser(JSON.parse(user));
        } else {
            this.updateUIForGuestUser();
        }
    }

    updateUIForAuthenticatedUser(user) {
        const userMenu = document.getElementById('userMenu');
        const authButtons = document.getElementById('authButtons');
        const userAvatar = document.getElementById('userAvatar');

        if (userMenu && authButtons) {
            userMenu.style.display = 'block';
            authButtons.style.display = 'none';
            
            if (userAvatar && user.avatar_url) {
                userAvatar.src = user.avatar_url;
            }
        }
    }

    updateUIForGuestUser() {
        const userMenu = document.getElementById('userMenu');
        const authButtons = document.getElementById('authButtons');

        if (userMenu && authButtons) {
            userMenu.style.display = 'none';
            authButtons.style.display = 'block';
        }
    }

    async loadInitialData() {
        try {
            // Load featured courses
            await this.loadFeaturedCourses();
            
            // Load categories
            await this.loadCategories();
            
            // Load bestseller courses
            await this.loadBestsellerCourses();
            
            // Update cart count
            await this.updateCartCount();
            
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showToast('Có lỗi xảy ra khi tải dữ liệu', 'error');
        }
    }

    async loadFeaturedCourses() {
        try {
            const container = document.getElementById('featuredCourses');
            if (!container) return;

            const courses = await api.getFeaturedCourses(8);
            container.innerHTML = this.renderCourses(courses);
        } catch (error) {
            console.error('Error loading featured courses:', error);
            document.getElementById('featuredCourses').innerHTML = 
                '<div class="col-12 text-center"><p class="text-muted">Không thể tải khóa học nổi bật</p></div>';
        }
    }

    async loadCategories() {
        try {
            const container = document.getElementById('categoriesGrid');
            if (!container) return;

            const categories = await api.getCategories();
            container.innerHTML = this.renderCategories(categories.slice(0, 8));
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    async loadBestsellerCourses() {
        try {
            const container = document.getElementById('bestsellerCourses');
            if (!container) return;

            const courses = await api.getBestsellerCourses(8);
            container.innerHTML = this.renderCourses(courses);
        } catch (error) {
            console.error('Error loading bestseller courses:', error);
        }
    }

    renderCourses(courses) {
        if (!courses || courses.length === 0) {
            return '<div class="col-12 text-center"><p class="text-muted">Không có khóa học nào</p></div>';
        }

        return courses.map(course => `
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="course-card">
                    <div class="position-relative">
                        <img src="${course.thumbnail_url || '/assets/images/course-placeholder.jpg'}" 
                             alt="${course.title}" class="card-img-top">
                        ${course.is_bestseller ? '<span class="badge badge-bestseller position-absolute top-0 start-0 m-2">Bestseller</span>' : ''}
                        ${course.is_featured ? '<span class="badge badge-featured position-absolute top-0 end-0 m-2">Nổi bật</span>' : ''}
                    </div>
                    <div class="course-card-body">
                        <div class="course-badges mb-2">
                            ${course.level ? `<span class="badge bg-secondary">${CONFIG.COURSE.LEVELS[course.level] || course.level}</span>` : ''}
                        </div>
                        <h5 class="course-title">
                            <a href="/course/${course.slug}">${course.title}</a>
                        </h5>
                        <p class="course-instructor">
                            <i class="fas fa-user me-1"></i>${course.instructor_name}
                        </p>
                        <div class="course-rating">
                            <div class="rating-stars">
                                ${this.renderStars(course.rating)}
                            </div>
                            <span class="rating-text">${course.rating} (${course.total_reviews} đánh giá)</span>
                        </div>
                        <div class="course-price">
                            <span class="current-price">${this.formatPrice(course.price)}</span>
                            ${course.original_price && course.original_price > course.price ? 
                                `<span class="original-price">${this.formatPrice(course.original_price)}</span>
                                 <span class="discount-badge">-${Math.round((1 - course.price / course.original_price) * 100)}%</span>` : ''}
                        </div>
                        <div class="course-meta">
                            <span><i class="fas fa-clock me-1"></i>${course.duration_hours}h</span>
                            <span><i class="fas fa-users me-1"></i>${course.total_students}</span>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary flex-grow-1 add-to-cart" data-course-id="${course.id}">
                                <i class="fas fa-shopping-cart me-1"></i>Thêm vào giỏ
                            </button>
                            <button class="btn btn-outline-secondary add-to-wishlist" data-course-id="${course.id}">
                                <i class="far fa-heart"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderCategories(categories) {
        const icons = {
            'kinh-doanh': 'fas fa-briefcase',
            'ngoai-ngu': 'fas fa-language',
            'thiet-ke': 'fas fa-paint-brush',
            'ky-nang': 'fas fa-users',
            'lap-trinh-cntt': 'fas fa-code',
            'marketing': 'fas fa-bullhorn',
            'tin-hoc-van-phong': 'fas fa-laptop',
            'am-nhac': 'fas fa-music'
        };

        return categories.map(category => `
            <div class="col-lg-3 col-md-6 mb-4">
                <a href="/courses?category=${category.slug}" class="category-card">
                    <div class="category-icon">
                        <i class="${icons[category.slug] || 'fas fa-folder'}"></i>
                    </div>
                    <h5 class="category-name">${category.name}</h5>
                    <p class="category-count">${category.courses_count || 0} khóa học</p>
                </a>
            </div>
        `).join('');
    }

    renderStars(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

        let stars = '';
        for (let i = 0; i < fullStars; i++) {
            stars += '<i class="fas fa-star"></i>';
        }
        if (hasHalfStar) {
            stars += '<i class="fas fa-star-half-alt"></i>';
        }
        for (let i = 0; i < emptyStars; i++) {
            stars += '<i class="far fa-star"></i>';
        }
        return stars;
    }

    formatPrice(price) {
        if (price === 0) return 'Miễn phí';
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(price);
    }

    async handleSearch(e) {
        e.preventDefault();
        const searchInput = document.getElementById('searchInput');
        const query = searchInput.value.trim();
        
        if (query) {
            this.navigate(`/search?q=${encodeURIComponent(query)}`);
        }
    }

    async handleAddToCart(e) {
        e.preventDefault();
        const courseId = e.target.dataset.courseId;
        
        if (!this.isAuthenticated()) {
            this.showToast('Vui lòng đăng nhập để thêm vào giỏ hàng', 'warning');
            this.navigate('/login');
            return;
        }

        try {
            await api.addToCart(courseId);
            this.showToast(CONFIG.SUCCESS.CART_ADD, 'success');
            await this.updateCartCount();
        } catch (error) {
            this.showToast(error.message, 'error');
        }
    }

    async handleAddToWishlist(e) {
        e.preventDefault();
        const courseId = e.target.dataset.courseId;
        
        if (!this.isAuthenticated()) {
            this.showToast('Vui lòng đăng nhập để thêm vào danh sách yêu thích', 'warning');
            this.navigate('/login');
            return;
        }

        try {
            await api.addToWishlist(courseId);
            this.showToast(CONFIG.SUCCESS.WISHLIST_ADD, 'success');
            
            // Update icon
            const icon = e.target.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-heart';
                e.target.classList.add('text-danger');
            }
        } catch (error) {
            this.showToast(error.message, 'error');
        }
    }

    async updateCartCount() {
        if (!this.isAuthenticated()) return;

        try {
            const cart = await api.getCart();
            const cartCount = document.getElementById('cartCount');
            if (cartCount) {
                cartCount.textContent = cart.items ? cart.items.length : 0;
            }
        } catch (error) {
            console.error('Error updating cart count:', error);
        }
    }

    isAuthenticated() {
        return !!localStorage.getItem(CONFIG.AUTH.TOKEN_KEY);
    }

    navigate(url) {
        // Simple client-side routing
        window.history.pushState({}, '', url);
        this.handleRoute(url);
    }

    handleRoute(url) {
        // Basic routing logic
        const path = url.split('?')[0];
        
        switch (path) {
            case '/':
                // Already on home page
                break;
            case '/courses':
                this.loadCoursesPage();
                break;
            case '/login':
                this.loadLoginPage();
                break;
            case '/register':
                this.loadRegisterPage();
                break;
            default:
                if (path.startsWith('/course/')) {
                    this.loadCourseDetailPage(path.split('/')[2]);
                }
                break;
        }
    }

    initializeComponents() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    showToast(message, type = 'info') {
        // Create toast element
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;

        // Add to toast container
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '9999';
            document.body.appendChild(toastContainer);
        }

        toastContainer.appendChild(toast);

        // Show toast
        const bsToast = new bootstrap.Toast(toast, {
            delay: CONFIG.UI.TOAST_DURATION
        });
        bsToast.show();

        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
}

// Global logout function
window.logout = async function() {
    try {
        await api.logout();
        localStorage.removeItem(CONFIG.AUTH.TOKEN_KEY);
        localStorage.removeItem(CONFIG.AUTH.REFRESH_TOKEN_KEY);
        localStorage.removeItem(CONFIG.AUTH.USER_KEY);
        
        window.location.reload();
    } catch (error) {
        console.error('Logout error:', error);
        // Force logout even if API call fails
        localStorage.clear();
        window.location.reload();
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new UnicaApp();
});

// Handle browser back/forward buttons
window.addEventListener('popstate', (e) => {
    window.app.handleRoute(window.location.pathname);
});

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, DECIMAL, ForeignKey, Table
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from ..core.database import Base


# Association table for course-tag many-to-many relationship
course_tags = Table(
    'course_tags',
    Base.metadata,
    Column('course_id', UUID(as_uuid=True), ForeignKey('courses.id', ondelete='CASCADE'), primary_key=True),
    Column('tag_id', UUID(as_uuid=True), ForeignKey('tags.id', ondelete='CASCADE'), primary_key=True)
)


class Course(Base):
    __tablename__ = "courses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(500), nullable=False)
    slug = Column(String(500), unique=True, nullable=False, index=True)
    description = Column(Text)
    short_description = Column(Text)
    thumbnail_url = Column(String(500))
    trailer_url = Column(String(500))
    
    # Foreign keys
    instructor_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey('categories.id'), nullable=False)
    
    # Course details
    level = Column(String(20), default="beginner")  # beginner, intermediate, advanced
    language = Column(String(10), default="vi")
    price = Column(DECIMAL(10, 2), default=0)
    original_price = Column(DECIMAL(10, 2))
    duration_hours = Column(Integer, default=0)
    total_lessons = Column(Integer, default=0)
    total_students = Column(Integer, default=0)
    rating = Column(DECIMAL(3, 2), default=0)
    total_reviews = Column(Integer, default=0)
    
    # Status flags
    is_published = Column(Boolean, default=False)
    is_featured = Column(Boolean, default=False)
    is_bestseller = Column(Boolean, default=False)
    
    # Additional info
    requirements = Column(Text)
    what_you_learn = Column(Text)
    target_audience = Column(Text)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    instructor = relationship("User", foreign_keys=[instructor_id])
    category = relationship("Category", back_populates="courses")
    tags = relationship("Tag", secondary=course_tags, back_populates="courses")
    sections = relationship("Section", back_populates="course", cascade="all, delete-orphan")
    enrollments = relationship("Enrollment", back_populates="course")
    reviews = relationship("Review", back_populates="course")
    
    def __repr__(self):
        return f"<Course(id={self.id}, title={self.title}, instructor_id={self.instructor_id})>"
    
    def to_dict(self, include_instructor=True, include_category=True):
        data = {
            "id": str(self.id),
            "title": self.title,
            "slug": self.slug,
            "description": self.description,
            "short_description": self.short_description,
            "thumbnail_url": self.thumbnail_url,
            "trailer_url": self.trailer_url,
            "level": self.level,
            "language": self.language,
            "price": float(self.price) if self.price else 0,
            "original_price": float(self.original_price) if self.original_price else None,
            "duration_hours": self.duration_hours,
            "total_lessons": self.total_lessons,
            "total_students": self.total_students,
            "rating": float(self.rating) if self.rating else 0,
            "total_reviews": self.total_reviews,
            "is_published": self.is_published,
            "is_featured": self.is_featured,
            "is_bestseller": self.is_bestseller,
            "requirements": self.requirements,
            "what_you_learn": self.what_you_learn,
            "target_audience": self.target_audience,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_instructor and self.instructor:
            data["instructor"] = {
                "id": str(self.instructor.id),
                "full_name": self.instructor.full_name,
                "avatar_url": self.instructor.avatar_url,
                "bio": self.instructor.bio
            }
        
        if include_category and self.category:
            data["category"] = {
                "id": str(self.category.id),
                "name": self.category.name,
                "slug": self.category.slug
            }
        
        return data
    
    @property
    def discount_percentage(self):
        if self.original_price and self.original_price > 0:
            return round((1 - float(self.price) / float(self.original_price)) * 100)
        return 0




{"name": "unica-clone-realtime", "version": "1.0.0", "description": "Realtime server for Unica Clone using Node.js and Socket.io", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "keywords": ["nodejs", "socket.io", "realtime", "chat", "notifications", "live-streaming"], "author": "Unica Clone Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "jsonwebtoken": "^9.0.2", "redis": "^4.6.10", "axios": "^1.6.2", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}
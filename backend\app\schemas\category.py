from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime


class CategoryBase(BaseModel):
    name: str
    description: Optional[str] = None
    icon_url: Optional[str] = None
    parent_id: Optional[str] = None
    sort_order: int = 0
    is_active: bool = True


class CategoryCreate(CategoryBase):
    slug: Optional[str] = None
    
    @validator('slug', pre=True, always=True)
    def generate_slug(cls, v, values):
        if not v and 'name' in values:
            import re
            # Simple slug generation
            slug = re.sub(r'[^\w\s-]', '', values['name'].lower())
            slug = re.sub(r'[-\s]+', '-', slug)
            return slug.strip('-')
        return v


class CategoryUpdate(BaseModel):
    name: Optional[str] = None
    slug: Optional[str] = None
    description: Optional[str] = None
    icon_url: Optional[str] = None
    parent_id: Optional[str] = None
    sort_order: Optional[int] = None
    is_active: Optional[bool] = None


class CategoryResponse(CategoryBase):
    id: str
    slug: str
    created_at: datetime
    courses_count: Optional[int] = 0
    children: Optional[List['CategoryResponse']] = []
    
    class Config:
        from_attributes = True


# Update forward reference
CategoryResponse.model_rebuild()

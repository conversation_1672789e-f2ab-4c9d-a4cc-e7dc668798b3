// Configuration for Unica Clone Frontend

const CONFIG = {
    // API Configuration
    API: {
        BASE_URL: 'http://localhost:8000/api/v1',
        TIMEOUT: 10000, // 10 seconds
        RETRY_ATTEMPTS: 3
    },
    
    // Realtime Configuration
    REALTIME: {
        URL: 'http://localhost:3001',
        OPTIONS: {
            transports: ['websocket', 'polling'],
            timeout: 5000,
            reconnection: true,
            reconnectionAttempts: 5,
            reconnectionDelay: 1000
        }
    },
    
    // Authentication
    AUTH: {
        TOKEN_KEY: 'unica_access_token',
        REFRESH_TOKEN_KEY: 'unica_refresh_token',
        USER_KEY: 'unica_user',
        TOKEN_EXPIRY_BUFFER: 300 // 5 minutes before expiry
    },
    
    // Pagination
    PAGINATION: {
        DEFAULT_PAGE_SIZE: 12,
        MAX_PAGE_SIZE: 50
    },
    
    // File Upload
    UPLOAD: {
        MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
        ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
        ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    },
    
    // UI Configuration
    UI: {
        TOAST_DURATION: 5000,
        LOADING_DELAY: 300,
        ANIMATION_DURATION: 300,
        DEBOUNCE_DELAY: 500
    },
    
    // Course Configuration
    COURSE: {
        LEVELS: {
            'beginner': 'Cơ bản',
            'intermediate': 'Trung cấp',
            'advanced': 'Nâng cao'
        },
        LANGUAGES: {
            'vi': 'Tiếng Việt',
            'en': 'English'
        }
    },
    
    // Cache Configuration
    CACHE: {
        CATEGORIES_TTL: 3600000, // 1 hour
        COURSES_TTL: 300000, // 5 minutes
        USER_TTL: 1800000 // 30 minutes
    },
    
    // Error Messages
    ERRORS: {
        NETWORK: 'Lỗi kết nối mạng. Vui lòng thử lại.',
        UNAUTHORIZED: 'Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.',
        FORBIDDEN: 'Bạn không có quyền thực hiện hành động này.',
        NOT_FOUND: 'Không tìm thấy dữ liệu yêu cầu.',
        SERVER_ERROR: 'Lỗi máy chủ. Vui lòng thử lại sau.',
        VALIDATION: 'Dữ liệu không hợp lệ. Vui lòng kiểm tra lại.',
        UPLOAD_SIZE: 'File quá lớn. Kích thước tối đa cho phép là 10MB.',
        UPLOAD_TYPE: 'Định dạng file không được hỗ trợ.'
    },
    
    // Success Messages
    SUCCESS: {
        LOGIN: 'Đăng nhập thành công!',
        REGISTER: 'Đăng ký thành công!',
        LOGOUT: 'Đăng xuất thành công!',
        PROFILE_UPDATE: 'Cập nhật hồ sơ thành công!',
        PASSWORD_CHANGE: 'Đổi mật khẩu thành công!',
        COURSE_ENROLL: 'Đăng ký khóa học thành công!',
        CART_ADD: 'Đã thêm vào giỏ hàng!',
        CART_REMOVE: 'Đã xóa khỏi giỏ hàng!',
        WISHLIST_ADD: 'Đã thêm vào danh sách yêu thích!',
        WISHLIST_REMOVE: 'Đã xóa khỏi danh sách yêu thích!'
    },
    
    // Routes
    ROUTES: {
        HOME: '/',
        COURSES: '/courses',
        COURSE_DETAIL: '/course',
        LOGIN: '/login',
        REGISTER: '/register',
        PROFILE: '/profile',
        MY_COURSES: '/my-courses',
        CART: '/cart',
        CHECKOUT: '/checkout',
        WISHLIST: '/wishlist',
        SEARCH: '/search',
        CATEGORIES: '/categories',
        INSTRUCTORS: '/instructors',
        LIVE: '/live',
        HELP: '/help',
        ABOUT: '/about',
        CONTACT: '/contact'
    },
    
    // Social Media
    SOCIAL: {
        FACEBOOK: 'https://facebook.com/unica-clone',
        YOUTUBE: 'https://youtube.com/unica-clone',
        TIKTOK: 'https://tiktok.com/@unica-clone',
        INSTAGRAM: 'https://instagram.com/unica-clone'
    },
    
    // Contact Information
    CONTACT: {
        PHONE: '1900 1568',
        EMAIL: '<EMAIL>',
        ADDRESS: '123 Đường ABC, Quận 1, TP.HCM'
    },
    
    // Feature Flags
    FEATURES: {
        LIVE_STREAMING: true,
        CHAT: true,
        NOTIFICATIONS: true,
        OFFLINE_MODE: false,
        DARK_MODE: false,
        MULTI_LANGUAGE: false
    },
    
    // Analytics
    ANALYTICS: {
        GOOGLE_ANALYTICS_ID: 'GA_MEASUREMENT_ID',
        FACEBOOK_PIXEL_ID: 'FB_PIXEL_ID'
    },
    
    // Development
    DEBUG: true,
    LOG_LEVEL: 'info' // 'debug', 'info', 'warn', 'error'
};

// Environment-specific overrides
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    CONFIG.DEBUG = true;
    CONFIG.LOG_LEVEL = 'debug';
} else {
    CONFIG.DEBUG = false;
    CONFIG.LOG_LEVEL = 'error';
    CONFIG.API.BASE_URL = 'https://api.unica-clone.com/api/v1';
    CONFIG.REALTIME.URL = 'https://realtime.unica-clone.com';
}

// Freeze configuration to prevent modifications
Object.freeze(CONFIG);

// Export for use in other modules
window.CONFIG = CONFIG;

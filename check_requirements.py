#!/usr/bin/env python3
"""
Script kiểm tra yêu cầu hệ thống cho Unica Clone
"""

import subprocess
import sys
import os

def check_command(command, name, install_guide=""):
    """Kiểm tra xem command có tồn tại không"""
    try:
        result = subprocess.run([command, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            version = result.stdout.strip().split('\n')[0]
            print(f"✅ {name}: {version}")
            return True
        else:
            print(f"❌ {name}: Không tìm thấy")
            if install_guide:
                print(f"   💡 Cài đặt: {install_guide}")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError):
        print(f"❌ {name}: Không tìm thấy")
        if install_guide:
            print(f"   💡 Cài đặt: {install_guide}")
        return False

def check_python_packages():
    """Kiểm tra Python packages"""
    required_packages = ['fastapi', 'uvicorn', 'sqlalchemy', 'psycopg2-binary']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ Python package: {package}")
        except ImportError:
            print(f"❌ Python package: {package} - Chưa cài đặt")
            missing_packages.append(package)
    
    return missing_packages

def check_services():
    """Kiểm tra các services đang chạy"""
    services = {
        'postgresql': ['pg_isready', 'PostgreSQL'],
        'redis': ['redis-cli', 'Redis']
    }
    
    for service, (command, name) in services.items():
        try:
            if service == 'postgresql':
                result = subprocess.run([command], capture_output=True, timeout=5)
                if result.returncode == 0:
                    print(f"✅ {name}: Đang chạy")
                else:
                    print(f"⚠️  {name}: Không chạy hoặc không thể kết nối")
            elif service == 'redis':
                result = subprocess.run([command, 'ping'], capture_output=True, timeout=5)
                if b'PONG' in result.stdout:
                    print(f"✅ {name}: Đang chạy")
                else:
                    print(f"⚠️  {name}: Không chạy")
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print(f"⚠️  {name}: Không thể kiểm tra")

def main():
    print("🔍 Kiểm tra yêu cầu hệ thống cho Unica Clone")
    print("=" * 50)
    
    # Kiểm tra Python
    python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    if sys.version_info >= (3, 8):
        print(f"✅ Python: {python_version}")
    else:
        print(f"❌ Python: {python_version} (Cần Python 3.8+)")
        return False
    
    # Kiểm tra Node.js
    node_ok = check_command("node", "Node.js", "https://nodejs.org/")
    
    # Kiểm tra npm
    npm_ok = check_command("npm", "npm", "Đi kèm với Node.js")
    
    # Kiểm tra PostgreSQL
    pg_ok = check_command("psql", "PostgreSQL", "https://postgresql.org/download/")
    
    # Kiểm tra Redis
    redis_ok = check_command("redis-server", "Redis", "https://redis.io/download")
    
    print("\n🔍 Kiểm tra Python packages:")
    missing_packages = check_python_packages()
    
    print("\n🔍 Kiểm tra services:")
    check_services()
    
    print("\n" + "=" * 50)
    
    if not all([node_ok, npm_ok]):
        print("❌ Thiếu Node.js hoặc npm")
        print("💡 Tải và cài đặt từ: https://nodejs.org/")
        return False
    
    if not pg_ok:
        print("❌ Thiếu PostgreSQL")
        print("💡 Tải và cài đặt từ: https://postgresql.org/download/")
        return False
    
    if missing_packages:
        print(f"❌ Thiếu Python packages: {', '.join(missing_packages)}")
        print("💡 Chạy: pip install -r backend/requirements.txt")
        return False
    
    print("✅ Tất cả yêu cầu đã được đáp ứng!")
    print("🚀 Bạn có thể chạy dự án bằng: python run.py")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

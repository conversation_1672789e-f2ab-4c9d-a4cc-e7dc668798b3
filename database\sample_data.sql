-- Sample data for Unica Clone
-- Insert sample data for testing

-- Insert sample categories
INSERT INTO categories (id, name, slug, description, icon_url, sort_order) VALUES
(uuid_generate_v4(), '<PERSON><PERSON> Do<PERSON>h', 'kinh-doanh', '<PERSON><PERSON><PERSON> kh<PERSON> học về kinh doanh và quản trị', '/icons/business.svg', 1),
(uuid_generate_v4(), 'Ngoại Ngữ', 'ngoai-ngu', '<PERSON>ọc ngoại ngữ từ cơ bản đến nâng cao', '/icons/language.svg', 2),
(uuid_generate_v4(), '<PERSON><PERSON><PERSON><PERSON>ế', 'thiet-ke', 'Thiế<PERSON> kế đồ họa, web, UI/UX', '/icons/design.svg', 3),
(uuid_generate_v4(), 'Kỹ Năng', 'ky-nang', '<PERSON><PERSON><PERSON> triển kỹ năng mềm', '/icons/skills.svg', 4),
(uuid_generate_v4(), 'Lập Trình & CNTT', 'lap-trinh-cntt', 'Lập trình và công nghệ thông tin', '/icons/programming.svg', 5),
(uuid_generate_v4(), 'Marketing', 'marketing', 'Digital Marketing và quảng cáo', '/icons/marketing.svg', 6),
(uuid_generate_v4(), 'Tin Học Văn Phòng', 'tin-hoc-van-phong', 'Excel, Word, PowerPoint', '/icons/office.svg', 7),
(uuid_generate_v4(), 'Âm Nhạc', 'am-nhac', 'Học nhạc cụ và thanh nhạc', '/icons/music.svg', 8);

-- Insert sample tags
INSERT INTO tags (name, slug) VALUES
('Python', 'python'),
('JavaScript', 'javascript'),
('React', 'react'),
('Node.js', 'nodejs'),
('FastAPI', 'fastapi'),
('PostgreSQL', 'postgresql'),
('HTML/CSS', 'html-css'),
('Bootstrap', 'bootstrap'),
('jQuery', 'jquery'),
('Git', 'git'),
('Docker', 'docker'),
('AWS', 'aws'),
('Machine Learning', 'machine-learning'),
('Data Science', 'data-science'),
('Web Development', 'web-development'),
('Mobile Development', 'mobile-development'),
('UI/UX Design', 'ui-ux-design'),
('Photoshop', 'photoshop'),
('Illustrator', 'illustrator'),
('Figma', 'figma'),
('Digital Marketing', 'digital-marketing'),
('SEO', 'seo'),
('Facebook Ads', 'facebook-ads'),
('Google Ads', 'google-ads'),
('Content Marketing', 'content-marketing'),
('Excel', 'excel'),
('PowerPoint', 'powerpoint'),
('Word', 'word'),
('Guitar', 'guitar'),
('Piano', 'piano'),
('English', 'english'),
('Japanese', 'japanese'),
('Korean', 'korean'),
('Chinese', 'chinese'),
('Business', 'business'),
('Leadership', 'leadership'),
('Communication', 'communication'),
('Presentation', 'presentation'),
('Time Management', 'time-management'),
('Project Management', 'project-management');

-- Insert sample users
INSERT INTO users (id, email, password_hash, full_name, phone, bio, role) VALUES
-- Admin user
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'Admin User', '0901234567', 'System Administrator', 'admin'),

-- Instructors
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'Nguyễn Văn A', '0901234568', 'Chuyên gia lập trình với 10 năm kinh nghiệm', 'instructor'),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'Trần Thị B', '0901234569', 'Giảng viên thiết kế đồ họa', 'instructor'),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'Lê Văn C', '0901234570', 'Chuyên gia Digital Marketing', 'instructor'),

-- Students
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'Phạm Văn D', '0901234571', 'Sinh viên IT', 'student'),
(uuid_generate_v4(), '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/A5/jF3kkS', 'Hoàng Thị E', '0901234572', 'Nhân viên văn phòng', 'student');

-- Insert sample courses
INSERT INTO courses (id, title, slug, description, short_description, instructor_id, category_id, level, price, original_price, duration_hours, total_lessons, rating, total_reviews, is_published, is_featured, is_bestseller, requirements, what_you_learn, target_audience) 
SELECT 
    uuid_generate_v4(),
    'Lập trình Python từ cơ bản đến nâng cao',
    'lap-trinh-python-co-ban-nang-cao',
    'Khóa học Python toàn diện từ cơ bản đến nâng cao, bao gồm web development với FastAPI, data science và machine learning.',
    'Học Python từ zero đến hero với các dự án thực tế',
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    (SELECT id FROM categories WHERE slug = 'lap-trinh-cntt'),
    'beginner',
    299000,
    599000,
    25,
    120,
    4.8,
    156,
    true,
    true,
    true,
    'Máy tính có kết nối internet, không cần kiến thức lập trình trước đó',
    'Nắm vững cú pháp Python, Xây dựng web API với FastAPI, Làm việc với database, Phân tích dữ liệu cơ bản',
    'Người mới bắt đầu học lập trình, Sinh viên IT, Người muốn chuyển nghề sang IT';

INSERT INTO courses (id, title, slug, description, short_description, instructor_id, category_id, level, price, original_price, duration_hours, total_lessons, rating, total_reviews, is_published, is_featured, is_bestseller, requirements, what_you_learn, target_audience) 
SELECT 
    uuid_generate_v4(),
    'Thiết kế UI/UX với Figma',
    'thiet-ke-ui-ux-figma',
    'Học thiết kế giao diện người dùng chuyên nghiệp với Figma từ cơ bản đến nâng cao.',
    'Thiết kế UI/UX chuyên nghiệp với Figma',
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    (SELECT id FROM categories WHERE slug = 'thiet-ke'),
    'intermediate',
    399000,
    799000,
    18,
    85,
    4.7,
    89,
    true,
    false,
    true,
    'Máy tính, cài đặt Figma, có kiến thức cơ bản về thiết kế',
    'Thiết kế wireframe và prototype, Sử dụng thành thạo Figma, Hiểu nguyên lý UX design, Tạo design system',
    'Designer mới bắt đầu, Developer muốn học design, Người làm marketing';

INSERT INTO courses (id, title, slug, description, short_description, instructor_id, category_id, level, price, original_price, duration_hours, total_lessons, rating, total_reviews, is_published, is_featured, is_bestseller, requirements, what_you_learn, target_audience) 
SELECT 
    uuid_generate_v4(),
    'Facebook Marketing từ A-Z',
    'facebook-marketing-a-z',
    'Khóa học Facebook Marketing toàn diện, từ tạo fanpage đến chạy quảng cáo hiệu quả.',
    'Làm chủ Facebook Marketing và Facebook Ads',
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    (SELECT id FROM categories WHERE slug = 'marketing'),
    'beginner',
    199000,
    499000,
    12,
    60,
    4.6,
    234,
    true,
    true,
    false,
    'Tài khoản Facebook cá nhân, Ngân sách quảng cáo tối thiểu 500k',
    'Tạo và quản lý fanpage, Thiết lập Facebook Ads, Tối ưu hóa chi phí quảng cáo, Phân tích báo cáo',
    'Chủ shop online, Marketer mới bắt đầu, Doanh nghiệp nhỏ';

-- Insert sample coupons
INSERT INTO coupons (code, description, discount_type, discount_value, min_amount, max_uses, valid_from, valid_until, is_active) VALUES
('WELCOME10', 'Giảm 10% cho khách hàng mới', 'percentage', 10, 100000, 1000, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '30 days', true),
('SAVE50K', 'Giảm 50k cho đơn hàng từ 300k', 'fixed', 50000, 300000, 500, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '15 days', true),
('BLACKFRIDAY', 'Black Friday - Giảm 30%', 'percentage', 30, 200000, 2000, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP + INTERVAL '7 days', true);

-- Note: Password hash is for 'password123' - should be changed in production

from sqlalchemy import Column, String, Boolean, DateTime, Text, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from ..core.database import Base


class Lesson(Base):
    __tablename__ = "lessons"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    section_id = Column(UUID(as_uuid=True), ForeignKey('sections.id', ondelete='CASCADE'), nullable=False)
    title = Column(String(255), nullable=False)
    description = Column(Text)
    content_type = Column(String(20), default='video')  # video, text, quiz, assignment
    video_url = Column(String(500))
    video_duration = Column(Integer)  # in seconds
    content = Column(Text)
    sort_order = Column(Integer, nullable=False)
    is_preview = Column(Boolean, default=False)
    is_published = Column(Boolean, default=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    section = relationship("Section", back_populates="lessons")
    
    def __repr__(self):
        return f"<Lesson(id={self.id}, title={self.title}, section_id={self.section_id})>"
    
    def to_dict(self):
        return {
            "id": str(self.id),
            "section_id": str(self.section_id),
            "title": self.title,
            "description": self.description,
            "content_type": self.content_type,
            "video_url": self.video_url,
            "video_duration": self.video_duration,
            "content": self.content,
            "sort_order": self.sort_order,
            "is_preview": self.is_preview,
            "is_published": self.is_published,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }

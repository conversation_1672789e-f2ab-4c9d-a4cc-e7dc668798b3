from pydantic import BaseModel, validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from enum import Enum


class CourseLevel(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


class CourseLanguage(str, Enum):
    VI = "vi"
    EN = "en"


class CourseBase(BaseModel):
    title: str
    description: Optional[str] = None
    short_description: Optional[str] = None
    category_id: str
    level: CourseLevel = CourseLevel.BEGINNER
    language: CourseLanguage = CourseLanguage.VI
    price: Decimal = Decimal('0')
    original_price: Optional[Decimal] = None
    requirements: Optional[str] = None
    what_you_learn: Optional[str] = None
    target_audience: Optional[str] = None


class CourseCreate(CourseBase):
    slug: Optional[str] = None
    
    @validator('slug', pre=True, always=True)
    def generate_slug(cls, v, values):
        if not v and 'title' in values:
            import re
            # Simple slug generation
            slug = re.sub(r'[^\w\s-]', '', values['title'].lower())
            slug = re.sub(r'[-\s]+', '-', slug)
            return slug.strip('-')
        return v


class CourseUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    short_description: Optional[str] = None
    category_id: Optional[str] = None
    level: Optional[CourseLevel] = None
    language: Optional[CourseLanguage] = None
    price: Optional[Decimal] = None
    original_price: Optional[Decimal] = None
    thumbnail_url: Optional[str] = None
    trailer_url: Optional[str] = None
    requirements: Optional[str] = None
    what_you_learn: Optional[str] = None
    target_audience: Optional[str] = None
    is_published: Optional[bool] = None
    is_featured: Optional[bool] = None


class CoursePublish(BaseModel):
    is_published: bool


class CategoryResponse(BaseModel):
    id: str
    name: str
    slug: str
    
    class Config:
        from_attributes = True


class TagResponse(BaseModel):
    id: str
    name: str
    slug: str
    
    class Config:
        from_attributes = True


class InstructorResponse(BaseModel):
    id: str
    full_name: str
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    
    class Config:
        from_attributes = True


class CourseResponse(CourseBase):
    id: str
    slug: str
    thumbnail_url: Optional[str] = None
    trailer_url: Optional[str] = None
    instructor_id: str
    duration_hours: int
    total_lessons: int
    total_students: int
    rating: Decimal
    total_reviews: int
    is_published: bool
    is_featured: bool
    is_bestseller: bool
    created_at: datetime
    updated_at: datetime
    
    # Related objects
    instructor: Optional[InstructorResponse] = None
    category: Optional[CategoryResponse] = None
    tags: Optional[List[TagResponse]] = []
    
    class Config:
        from_attributes = True


class CourseListResponse(BaseModel):
    id: str
    title: str
    slug: str
    short_description: Optional[str] = None
    thumbnail_url: Optional[str] = None
    instructor_name: str
    category_name: str
    level: str
    price: Decimal
    original_price: Optional[Decimal] = None
    rating: Decimal
    total_reviews: int
    total_students: int
    duration_hours: int
    is_featured: bool
    is_bestseller: bool
    
    class Config:
        from_attributes = True


class CourseSearchFilters(BaseModel):
    category_id: Optional[str] = None
    level: Optional[CourseLevel] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    rating: Optional[int] = None
    language: Optional[CourseLanguage] = None
    is_free: Optional[bool] = None
    is_featured: Optional[bool] = None
    is_bestseller: Optional[bool] = None


class CourseSearchResponse(BaseModel):
    courses: List[CourseListResponse]
    total: int
    page: int
    size: int
    total_pages: int

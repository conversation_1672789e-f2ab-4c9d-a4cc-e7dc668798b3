const redisService = require('../services/redisService');

class LiveStreamHandler {
  constructor(io) {
    this.io = io;
  }

  handleConnection(socket) {
    // Join live session as participant
    socket.on('join_live_session', async (data) => {
      try {
        const { sessionId } = data;
        
        if (!sessionId) {
          socket.emit('error', { message: 'Session ID is required' });
          return;
        }

        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required for live sessions' });
          return;
        }

        // TODO: Verify user is enrolled in the course
        // TODO: Check if session exists and is active
        
        const sessionRoom = `live_session_${sessionId}`;
        await socket.join(sessionRoom);
        
        // Add participant to Redis
        await redisService.addUserToRoom(sessionRoom, socket.userId, socket.id);
        
        // Get current participants count
        const participants = await redisService.getRoomUsers(sessionRoom);
        
        // Notify others that user joined
        socket.to(sessionRoom).emit('participant_joined', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          participantsCount: participants.length,
          timestamp: new Date().toISOString()
        });
        
        socket.emit('joined_live_session', {
          sessionId,
          participantsCount: participants.length,
          message: 'Successfully joined live session'
        });
        
        console.log(`User ${socket.userId} joined live session ${sessionId}`);
      } catch (error) {
        console.error('Error joining live session:', error);
        socket.emit('error', { message: 'Failed to join live session' });
      }
    });

    // Leave live session
    socket.on('leave_live_session', async (data) => {
      try {
        const { sessionId } = data;
        
        if (!sessionId) {
          socket.emit('error', { message: 'Session ID is required' });
          return;
        }

        const sessionRoom = `live_session_${sessionId}`;
        await socket.leave(sessionRoom);
        
        // Remove participant from Redis
        await redisService.removeUserFromRoom(sessionRoom, socket.userId, socket.id);
        
        // Get updated participants count
        const participants = await redisService.getRoomUsers(sessionRoom);
        
        // Notify others that user left
        socket.to(sessionRoom).emit('participant_left', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          participantsCount: participants.length,
          timestamp: new Date().toISOString()
        });
        
        socket.emit('left_live_session', {
          sessionId,
          message: 'Successfully left live session'
        });
        
        console.log(`User ${socket.userId} left live session ${sessionId}`);
      } catch (error) {
        console.error('Error leaving live session:', error);
        socket.emit('error', { message: 'Failed to leave live session' });
      }
    });

    // Instructor starts live session
    socket.on('start_live_session', async (data) => {
      try {
        const { sessionId } = data;
        
        if (!sessionId) {
          socket.emit('error', { message: 'Session ID is required' });
          return;
        }

        // Check if user is instructor or admin
        if (socket.userRole !== 'instructor' && socket.userRole !== 'admin') {
          socket.emit('error', { message: 'Only instructors can start live sessions' });
          return;
        }

        const sessionRoom = `live_session_${sessionId}`;
        
        // TODO: Update session status in database
        
        // Notify all participants that session started
        this.io.to(sessionRoom).emit('live_session_started', {
          sessionId,
          instructorId: socket.userId,
          timestamp: new Date().toISOString()
        });
        
        console.log(`Live session ${sessionId} started by instructor ${socket.userId}`);
      } catch (error) {
        console.error('Error starting live session:', error);
        socket.emit('error', { message: 'Failed to start live session' });
      }
    });

    // Instructor ends live session
    socket.on('end_live_session', async (data) => {
      try {
        const { sessionId } = data;
        
        if (!sessionId) {
          socket.emit('error', { message: 'Session ID is required' });
          return;
        }

        // Check if user is instructor or admin
        if (socket.userRole !== 'instructor' && socket.userRole !== 'admin') {
          socket.emit('error', { message: 'Only instructors can end live sessions' });
          return;
        }

        const sessionRoom = `live_session_${sessionId}`;
        
        // TODO: Update session status in database
        
        // Notify all participants that session ended
        this.io.to(sessionRoom).emit('live_session_ended', {
          sessionId,
          instructorId: socket.userId,
          timestamp: new Date().toISOString()
        });
        
        console.log(`Live session ${sessionId} ended by instructor ${socket.userId}`);
      } catch (error) {
        console.error('Error ending live session:', error);
        socket.emit('error', { message: 'Failed to end live session' });
      }
    });

    // Send live session message/announcement
    socket.on('send_live_message', async (data) => {
      try {
        const { sessionId, message, messageType = 'text' } = data;
        
        if (!sessionId || !message) {
          socket.emit('error', { message: 'Session ID and message are required' });
          return;
        }

        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const sessionRoom = `live_session_${sessionId}`;
        
        const messageData = {
          sessionId,
          userId: socket.userId,
          userEmail: socket.userEmail,
          userRole: socket.userRole,
          message,
          messageType,
          timestamp: new Date().toISOString()
        };

        // Broadcast message to all participants
        this.io.to(sessionRoom).emit('live_session_message', messageData);
        
        console.log(`Live message sent in session ${sessionId} by user ${socket.userId}`);
      } catch (error) {
        console.error('Error sending live message:', error);
        socket.emit('error', { message: 'Failed to send live message' });
      }
    });

    // Raise hand in live session
    socket.on('raise_hand', async (data) => {
      try {
        const { sessionId } = data;
        
        if (!sessionId) {
          socket.emit('error', { message: 'Session ID is required' });
          return;
        }

        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const sessionRoom = `live_session_${sessionId}`;
        
        // Notify instructor about raised hand
        socket.to(sessionRoom).emit('hand_raised', {
          sessionId,
          userId: socket.userId,
          userEmail: socket.userEmail,
          timestamp: new Date().toISOString()
        });
        
        socket.emit('hand_raised_confirmed', {
          sessionId,
          message: 'Hand raised successfully'
        });
        
        console.log(`Hand raised in session ${sessionId} by user ${socket.userId}`);
      } catch (error) {
        console.error('Error raising hand:', error);
        socket.emit('error', { message: 'Failed to raise hand' });
      }
    });

    // Lower hand in live session
    socket.on('lower_hand', async (data) => {
      try {
        const { sessionId } = data;
        
        if (!sessionId) {
          socket.emit('error', { message: 'Session ID is required' });
          return;
        }

        const sessionRoom = `live_session_${sessionId}`;
        
        socket.to(sessionRoom).emit('hand_lowered', {
          sessionId,
          userId: socket.userId,
          userEmail: socket.userEmail,
          timestamp: new Date().toISOString()
        });
        
        socket.emit('hand_lowered_confirmed', {
          sessionId,
          message: 'Hand lowered successfully'
        });
        
        console.log(`Hand lowered in session ${sessionId} by user ${socket.userId}`);
      } catch (error) {
        console.error('Error lowering hand:', error);
        socket.emit('error', { message: 'Failed to lower hand' });
      }
    });

    // Screen sharing events
    socket.on('start_screen_share', async (data) => {
      try {
        const { sessionId } = data;
        
        // Only instructors can share screen
        if (socket.userRole !== 'instructor' && socket.userRole !== 'admin') {
          socket.emit('error', { message: 'Only instructors can share screen' });
          return;
        }

        const sessionRoom = `live_session_${sessionId}`;
        
        socket.to(sessionRoom).emit('screen_share_started', {
          sessionId,
          instructorId: socket.userId,
          timestamp: new Date().toISOString()
        });
        
        console.log(`Screen sharing started in session ${sessionId} by instructor ${socket.userId}`);
      } catch (error) {
        console.error('Error starting screen share:', error);
        socket.emit('error', { message: 'Failed to start screen share' });
      }
    });

    socket.on('stop_screen_share', async (data) => {
      try {
        const { sessionId } = data;
        
        const sessionRoom = `live_session_${sessionId}`;
        
        socket.to(sessionRoom).emit('screen_share_stopped', {
          sessionId,
          instructorId: socket.userId,
          timestamp: new Date().toISOString()
        });
        
        console.log(`Screen sharing stopped in session ${sessionId} by instructor ${socket.userId}`);
      } catch (error) {
        console.error('Error stopping screen share:', error);
        socket.emit('error', { message: 'Failed to stop screen share' });
      }
    });
  }

  // Method to notify about upcoming live sessions
  async notifyUpcomingSession(sessionId, courseId, scheduledTime) {
    try {
      const courseRoom = `course_${courseId}`;
      
      this.io.to(courseRoom).emit('upcoming_live_session', {
        sessionId,
        courseId,
        scheduledTime,
        message: 'Live session starting soon',
        timestamp: new Date().toISOString()
      });
      
      console.log(`Notified about upcoming session ${sessionId} for course ${courseId}`);
      return true;
    } catch (error) {
      console.error('Error notifying upcoming session:', error);
      return false;
    }
  }
}

module.exports = LiveStreamHandler;

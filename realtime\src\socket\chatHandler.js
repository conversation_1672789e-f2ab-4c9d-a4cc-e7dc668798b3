const redisService = require('../services/redisService');
const { v4: uuidv4 } = require('uuid');

class ChatHandler {
  constructor(io) {
    this.io = io;
  }

  handleConnection(socket) {
    // Join course chat room
    socket.on('join_course_chat', async (data) => {
      try {
        const { courseId } = data;
        
        if (!courseId) {
          socket.emit('error', { message: 'Course ID is required' });
          return;
        }

        // TODO: Verify user is enrolled in course
        // For now, allow anyone to join
        
        const roomId = `course_chat_${courseId}`;
        await socket.join(roomId);
        
        // Add user to room in Redis
        await redisService.addUserToRoom(roomId, socket.userId, socket.id);
        
        // Get room users count
        const roomUsers = await redisService.getRoomUsers(roomId);
        
        // Notify user joined
        socket.to(roomId).emit('user_joined_chat', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          timestamp: new Date().toISOString()
        });
        
        socket.emit('joined_course_chat', {
          courseId,
          roomId,
          usersCount: roomUsers.length,
          message: 'Successfully joined course chat'
        });
        
        console.log(`User ${socket.userId} joined course chat ${courseId}`);
      } catch (error) {
        console.error('Error joining course chat:', error);
        socket.emit('error', { message: 'Failed to join course chat' });
      }
    });

    // Leave course chat room
    socket.on('leave_course_chat', async (data) => {
      try {
        const { courseId } = data;
        
        if (!courseId) {
          socket.emit('error', { message: 'Course ID is required' });
          return;
        }

        const roomId = `course_chat_${courseId}`;
        await socket.leave(roomId);
        
        // Remove user from room in Redis
        await redisService.removeUserFromRoom(roomId, socket.userId, socket.id);
        
        // Notify user left
        socket.to(roomId).emit('user_left_chat', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          timestamp: new Date().toISOString()
        });
        
        socket.emit('left_course_chat', {
          courseId,
          message: 'Successfully left course chat'
        });
        
        console.log(`User ${socket.userId} left course chat ${courseId}`);
      } catch (error) {
        console.error('Error leaving course chat:', error);
        socket.emit('error', { message: 'Failed to leave course chat' });
      }
    });

    // Send message to course chat
    socket.on('send_course_message', async (data) => {
      try {
        const { courseId, message, messageType = 'text' } = data;
        
        if (!courseId || !message) {
          socket.emit('error', { message: 'Course ID and message are required' });
          return;
        }

        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        // Validate message length
        if (message.length > 1000) {
          socket.emit('error', { message: 'Message too long (max 1000 characters)' });
          return;
        }

        const roomId = `course_chat_${courseId}`;
        
        // Check if user is in the room
        const userRooms = await redisService.getUserRooms(socket.userId);
        if (!userRooms.includes(roomId)) {
          socket.emit('error', { message: 'You must join the chat first' });
          return;
        }

        const messageData = {
          id: uuidv4(),
          courseId,
          userId: socket.userId,
          userEmail: socket.userEmail,
          message,
          messageType,
          timestamp: new Date().toISOString()
        };

        // TODO: Save message to database
        // For now, just broadcast to room
        
        // Broadcast message to all users in the room
        this.io.to(roomId).emit('new_course_message', messageData);
        
        console.log(`Message sent in course ${courseId} by user ${socket.userId}`);
      } catch (error) {
        console.error('Error sending course message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Join lesson Q&A room
    socket.on('join_lesson_qa', async (data) => {
      try {
        const { lessonId } = data;
        
        if (!lessonId) {
          socket.emit('error', { message: 'Lesson ID is required' });
          return;
        }

        const roomId = `lesson_qa_${lessonId}`;
        await socket.join(roomId);
        
        await redisService.addUserToRoom(roomId, socket.userId, socket.id);
        
        socket.emit('joined_lesson_qa', {
          lessonId,
          roomId,
          message: 'Successfully joined lesson Q&A'
        });
        
        console.log(`User ${socket.userId} joined lesson Q&A ${lessonId}`);
      } catch (error) {
        console.error('Error joining lesson Q&A:', error);
        socket.emit('error', { message: 'Failed to join lesson Q&A' });
      }
    });

    // Send Q&A message
    socket.on('send_qa_message', async (data) => {
      try {
        const { lessonId, question, isInstructor = false } = data;
        
        if (!lessonId || !question) {
          socket.emit('error', { message: 'Lesson ID and question are required' });
          return;
        }

        if (!socket.userId) {
          socket.emit('error', { message: 'Authentication required' });
          return;
        }

        const roomId = `lesson_qa_${lessonId}`;
        
        const qaData = {
          id: uuidv4(),
          lessonId,
          userId: socket.userId,
          userEmail: socket.userEmail,
          question,
          isInstructor: isInstructor && (socket.userRole === 'instructor' || socket.userRole === 'admin'),
          timestamp: new Date().toISOString()
        };

        // TODO: Save Q&A to database
        
        this.io.to(roomId).emit('new_qa_message', qaData);
        
        console.log(`Q&A message sent in lesson ${lessonId} by user ${socket.userId}`);
      } catch (error) {
        console.error('Error sending Q&A message:', error);
        socket.emit('error', { message: 'Failed to send Q&A message' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data) => {
      const { roomType, roomId } = data;
      if (roomType && roomId) {
        socket.to(`${roomType}_${roomId}`).emit('user_typing', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          isTyping: true
        });
      }
    });

    socket.on('typing_stop', (data) => {
      const { roomType, roomId } = data;
      if (roomType && roomId) {
        socket.to(`${roomType}_${roomId}`).emit('user_typing', {
          userId: socket.userId,
          userEmail: socket.userEmail,
          isTyping: false
        });
      }
    });

    // Handle disconnect
    socket.on('disconnect', async () => {
      try {
        // Remove user from all rooms
        if (socket.userId) {
          const userRooms = await redisService.getUserRooms(socket.userId);
          for (const roomId of userRooms) {
            await redisService.removeUserFromRoom(roomId, socket.userId, socket.id);
            
            // Notify room that user left
            socket.to(roomId).emit('user_left_chat', {
              userId: socket.userId,
              userEmail: socket.userEmail,
              timestamp: new Date().toISOString()
            });
          }
        }
      } catch (error) {
        console.error('Error handling chat disconnect:', error);
      }
    });
  }
}

module.exports = ChatHandler;

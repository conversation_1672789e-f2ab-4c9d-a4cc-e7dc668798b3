from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from typing import List, Optional

from ...core.database import get_db
from ...models.user import User
from ...models.course import Course
from ...models.category import Category
from ...models.tag import Tag
from ...schemas.course import (
    CourseCreate,
    CourseUpdate,
    CourseResponse,
    CourseListResponse,
    CourseSearchResponse,
    CourseSearchFilters,
    CoursePublish
)
from ...api.deps import (
    get_current_user,
    get_current_instructor,
    get_optional_current_user,
    CommonQueryParams
)

router = APIRouter()


@router.get("/", response_model=CourseSearchResponse)
def get_courses(
    params: CommonQueryParams = Depends(),
    filters: CourseSearchFilters = Depends(),
    db: Session = Depends(get_db)
):
    """Get courses with pagination and filtering"""
    query = db.query(Course).filter(Course.is_published == True)
    
    # Apply filters
    if filters.category_id:
        query = query.filter(Course.category_id == filters.category_id)
    
    if filters.level:
        query = query.filter(Course.level == filters.level)
    
    if filters.min_price is not None:
        query = query.filter(Course.price >= filters.min_price)
    
    if filters.max_price is not None:
        query = query.filter(Course.price <= filters.max_price)
    
    if filters.rating is not None:
        query = query.filter(Course.rating >= filters.rating)
    
    if filters.language:
        query = query.filter(Course.language == filters.language)
    
    if filters.is_free is not None:
        if filters.is_free:
            query = query.filter(Course.price == 0)
        else:
            query = query.filter(Course.price > 0)
    
    if filters.is_featured is not None:
        query = query.filter(Course.is_featured == filters.is_featured)
    
    if filters.is_bestseller is not None:
        query = query.filter(Course.is_bestseller == filters.is_bestseller)
    
    # Apply search
    if params.search:
        search_term = f"%{params.search}%"
        query = query.filter(
            or_(
                Course.title.ilike(search_term),
                Course.description.ilike(search_term),
                Course.short_description.ilike(search_term)
            )
        )
    
    # Apply sorting
    if params.sort_by:
        sort_column = getattr(Course, params.sort_by, None)
        if sort_column:
            if params.sort_order == "asc":
                query = query.order_by(asc(sort_column))
            else:
                query = query.order_by(desc(sort_column))
    else:
        # Default sorting by created_at desc
        query = query.order_by(desc(Course.created_at))
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    courses = query.offset(params.offset).limit(params.size).all()
    
    # Convert to response format
    course_list = []
    for course in courses:
        course_data = CourseListResponse(
            id=str(course.id),
            title=course.title,
            slug=course.slug,
            short_description=course.short_description,
            thumbnail_url=course.thumbnail_url,
            instructor_name=course.instructor.full_name if course.instructor else "",
            category_name=course.category.name if course.category else "",
            level=course.level,
            price=course.price,
            original_price=course.original_price,
            rating=course.rating,
            total_reviews=course.total_reviews,
            total_students=course.total_students,
            duration_hours=course.duration_hours,
            is_featured=course.is_featured,
            is_bestseller=course.is_bestseller
        )
        course_list.append(course_data)
    
    total_pages = (total + params.size - 1) // params.size
    
    return CourseSearchResponse(
        courses=course_list,
        total=total,
        page=params.page,
        size=params.size,
        total_pages=total_pages
    )


@router.get("/featured", response_model=List[CourseListResponse])
def get_featured_courses(
    limit: int = Query(default=8, le=20),
    db: Session = Depends(get_db)
):
    """Get featured courses"""
    courses = db.query(Course).filter(
        and_(Course.is_published == True, Course.is_featured == True)
    ).order_by(desc(Course.created_at)).limit(limit).all()
    
    course_list = []
    for course in courses:
        course_data = CourseListResponse(
            id=str(course.id),
            title=course.title,
            slug=course.slug,
            short_description=course.short_description,
            thumbnail_url=course.thumbnail_url,
            instructor_name=course.instructor.full_name if course.instructor else "",
            category_name=course.category.name if course.category else "",
            level=course.level,
            price=course.price,
            original_price=course.original_price,
            rating=course.rating,
            total_reviews=course.total_reviews,
            total_students=course.total_students,
            duration_hours=course.duration_hours,
            is_featured=course.is_featured,
            is_bestseller=course.is_bestseller
        )
        course_list.append(course_data)
    
    return course_list


@router.get("/bestsellers", response_model=List[CourseListResponse])
def get_bestseller_courses(
    limit: int = Query(default=8, le=20),
    db: Session = Depends(get_db)
):
    """Get bestseller courses"""
    courses = db.query(Course).filter(
        and_(Course.is_published == True, Course.is_bestseller == True)
    ).order_by(desc(Course.total_students)).limit(limit).all()
    
    course_list = []
    for course in courses:
        course_data = CourseListResponse(
            id=str(course.id),
            title=course.title,
            slug=course.slug,
            short_description=course.short_description,
            thumbnail_url=course.thumbnail_url,
            instructor_name=course.instructor.full_name if course.instructor else "",
            category_name=course.category.name if course.category else "",
            level=course.level,
            price=course.price,
            original_price=course.original_price,
            rating=course.rating,
            total_reviews=course.total_reviews,
            total_students=course.total_students,
            duration_hours=course.duration_hours,
            is_featured=course.is_featured,
            is_bestseller=course.is_bestseller
        )
        course_list.append(course_data)
    
    return course_list


@router.get("/{course_id}", response_model=CourseResponse)
def get_course(
    course_id: str,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_optional_current_user)
):
    """Get course by ID"""
    course = db.query(Course).filter(Course.id == course_id).first()
    if not course:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Course not found"
        )
    
    # Check if course is published or user is instructor/admin
    if not course.is_published:
        if not current_user or (
            current_user.id != course.instructor_id and 
            not current_user.is_admin
        ):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )
    
    return CourseResponse.from_orm(course)


@router.post("/", response_model=CourseResponse)
def create_course(
    course_data: CourseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_instructor)
):
    """Create a new course"""
    # Check if slug already exists
    if course_data.slug:
        existing_course = db.query(Course).filter(Course.slug == course_data.slug).first()
        if existing_course:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Course slug already exists"
            )
    
    # Verify category exists
    category = db.query(Category).filter(Category.id == course_data.category_id).first()
    if not category:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Category not found"
        )
    
    # Create course
    db_course = Course(
        **course_data.dict(),
        instructor_id=current_user.id
    )
    
    db.add(db_course)
    db.commit()
    db.refresh(db_course)
    
    return CourseResponse.from_orm(db_course)


@router.put("/{course_id}", response_model=CourseResponse)
def update_course(
    course_id: str,
    course_data: CourseUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_instructor)
):
    """Update course"""
    course = db.query(Course).filter(Course.id == course_id).first()
    if not course:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Course not found"
        )
    
    # Check permissions
    if course.instructor_id != current_user.id and not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Update course
    update_data = course_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(course, field, value)
    
    db.commit()
    db.refresh(course)
    
    return CourseResponse.from_orm(course)


@router.delete("/{course_id}")
def delete_course(
    course_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_instructor)
):
    """Delete course"""
    course = db.query(Course).filter(Course.id == course_id).first()
    if not course:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Course not found"
        )
    
    # Check permissions
    if course.instructor_id != current_user.id and not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    db.delete(course)
    db.commit()
    
    return {"message": "Course deleted successfully"}

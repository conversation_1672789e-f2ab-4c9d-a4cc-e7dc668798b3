from sqlalchemy import Column, String, DateTime, DECIMAL, Foreign<PERSON>ey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid
from ..core.database import Base


class Order(Base):
    __tablename__ = "orders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    total_amount = Column(DECIMAL(10, 2), nullable=False)
    discount_amount = Column(DECIMAL(10, 2), default=0)
    final_amount = Column(DECIMAL(10, 2), nullable=False)
    status = Column(String(20), default='pending')  # pending, completed, cancelled, refunded
    payment_method = Column(String(50))
    payment_id = Column(String(255))
    coupon_code = Column(String(50))
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    items = relationship("OrderItem", back_populates="order", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Order(id={self.id}, user_id={self.user_id}, status={self.status})>"
    
    def to_dict(self, include_items=True):
        data = {
            "id": str(self.id),
            "user_id": str(self.user_id),
            "total_amount": float(self.total_amount),
            "discount_amount": float(self.discount_amount),
            "final_amount": float(self.final_amount),
            "status": self.status,
            "payment_method": self.payment_method,
            "payment_id": self.payment_id,
            "coupon_code": self.coupon_code,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_items and self.items:
            data["items"] = [item.to_dict() for item in self.items]
        
        return data


class OrderItem(Base):
    __tablename__ = "order_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    order_id = Column(UUID(as_uuid=True), ForeignKey('orders.id', ondelete='CASCADE'), nullable=False)
    course_id = Column(UUID(as_uuid=True), ForeignKey('courses.id'), nullable=False)
    price = Column(DECIMAL(10, 2), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    order = relationship("Order", back_populates="items")
    course = relationship("Course", foreign_keys=[course_id])
    
    def __repr__(self):
        return f"<OrderItem(id={self.id}, order_id={self.order_id}, course_id={self.course_id})>"
    
    def to_dict(self, include_course=True):
        data = {
            "id": str(self.id),
            "order_id": str(self.order_id),
            "course_id": str(self.course_id),
            "price": float(self.price),
            "created_at": self.created_at.isoformat() if self.created_at else None
        }
        
        if include_course and self.course:
            data["course"] = {
                "id": str(self.course.id),
                "title": self.course.title,
                "thumbnail_url": self.course.thumbnail_url
            }
        
        return data

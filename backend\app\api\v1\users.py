from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...core.database import get_db
from ...core.security import get_password_hash, verify_password
from ...models.user import User
from ...schemas.user import (
    UserResponse, 
    UserUpdate, 
    UserChangePassword,
    UserProfile
)
from ...api.deps import get_current_user

router = APIRouter()


@router.get("/profile", response_model=UserProfile)
def get_user_profile(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get current user profile"""
    # TODO: Add course counts for instructors and enrollment counts for students
    profile_data = UserProfile.from_orm(current_user)
    
    if current_user.is_instructor:
        # Count instructor's courses
        from ...models.course import Course
        total_courses = db.query(Course).filter(Course.instructor_id == current_user.id).count()
        profile_data.total_courses = total_courses
        
        # Count total students across all courses
        # TODO: Implement this query
        profile_data.total_students = 0
    
    if current_user.is_student:
        # Count student's enrollments
        from ...models.enrollment import Enrollment
        total_enrollments = db.query(Enrollment).filter(Enrollment.user_id == current_user.id).count()
        profile_data.total_enrollments = total_enrollments
    
    return profile_data


@router.put("/profile", response_model=UserResponse)
def update_user_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user profile"""
    # Update user data
    update_data = user_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(current_user, field, value)
    
    db.commit()
    db.refresh(current_user)
    
    return UserResponse.from_orm(current_user)


@router.put("/change-password")
def change_password(
    password_data: UserChangePassword,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Change user password"""
    # Verify current password
    if not verify_password(password_data.current_password, current_user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    current_user.password_hash = get_password_hash(password_data.new_password)
    db.commit()
    
    return {"message": "Password changed successfully"}


@router.get("/courses")
def get_user_courses(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user's courses (enrollments for students, created courses for instructors)"""
    if current_user.is_instructor:
        # Get instructor's courses
        from ...models.course import Course
        courses = db.query(Course).filter(Course.instructor_id == current_user.id).all()
        return {
            "type": "instructor_courses",
            "courses": [course.to_dict() for course in courses]
        }
    else:
        # Get student's enrollments
        from ...models.enrollment import Enrollment
        from ...models.course import Course
        enrollments = db.query(Enrollment).join(Course).filter(
            Enrollment.user_id == current_user.id
        ).all()
        
        courses_data = []
        for enrollment in enrollments:
            course_data = enrollment.course.to_dict()
            course_data["enrollment"] = {
                "enrolled_at": enrollment.enrolled_at.isoformat() if enrollment.enrolled_at else None,
                "progress": float(enrollment.progress) if enrollment.progress else 0,
                "completed_at": enrollment.completed_at.isoformat() if enrollment.completed_at else None,
                "last_accessed_at": enrollment.last_accessed_at.isoformat() if enrollment.last_accessed_at else None
            }
            courses_data.append(course_data)
        
        return {
            "type": "enrolled_courses",
            "courses": courses_data
        }


@router.get("/dashboard")
def get_user_dashboard(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get user dashboard data"""
    dashboard_data = {
        "user": UserResponse.from_orm(current_user).dict(),
        "stats": {}
    }
    
    if current_user.is_instructor:
        from ...models.course import Course
        from ...models.enrollment import Enrollment
        
        # Instructor stats
        total_courses = db.query(Course).filter(Course.instructor_id == current_user.id).count()
        published_courses = db.query(Course).filter(
            Course.instructor_id == current_user.id,
            Course.is_published == True
        ).count()
        
        # Total students across all courses
        total_students = db.query(Enrollment).join(Course).filter(
            Course.instructor_id == current_user.id
        ).count()
        
        dashboard_data["stats"] = {
            "total_courses": total_courses,
            "published_courses": published_courses,
            "total_students": total_students,
            "total_revenue": 0  # TODO: Calculate from orders
        }
    
    elif current_user.is_student:
        from ...models.enrollment import Enrollment
        from ...models.lesson_progress import LessonProgress
        
        # Student stats
        total_enrollments = db.query(Enrollment).filter(Enrollment.user_id == current_user.id).count()
        completed_courses = db.query(Enrollment).filter(
            Enrollment.user_id == current_user.id,
            Enrollment.completed_at.isnot(None)
        ).count()
        
        # Total learning time (approximate)
        total_lessons_completed = db.query(LessonProgress).filter(
            LessonProgress.user_id == current_user.id,
            LessonProgress.is_completed == True
        ).count()
        
        dashboard_data["stats"] = {
            "total_enrollments": total_enrollments,
            "completed_courses": completed_courses,
            "total_lessons_completed": total_lessons_completed,
            "certificates_earned": completed_courses  # Assuming 1 certificate per completed course
        }
    
    return dashboard_data
